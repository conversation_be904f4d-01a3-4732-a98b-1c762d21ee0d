import * as Cesium from 'cesium'
/**
 * 多段测距 Hook
 * @param  viewer
 * @param  containerRef 地图外层容器（用于改光标）
 */
export function useMeasure(viewer, containerRef) {
  /* ---------- 全局响应式 ---------- */
  const isMeasuring = ref(false)
  const isSpaceMeasuring = ref(false) // 空间测距状态

  const CLOSE_ICON_URI = 'data:image/svg+xml;base64,' +
    btoa(`<svg t="1753345396406" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27506" width="200" height="200"><path d="M512 853.333a341.333 341.333 0 1 0 0-682.666 341.333 341.333 0 0 0 0 682.666z m0 85.334C276.352 938.667 85.333 747.648 85.333 512S276.352 85.333 512 85.333 938.667 276.352 938.667 512 747.648 938.667 512 938.667z" p-id="27507" fill="#d81e06"></path><path d="M374.315 315.733l328.832 328.832-60.331 60.331-328.832-328.875z" p-id="27508" fill="#d81e06"></path><path d="M706.56 375.595L377.728 704.469l-60.33-60.33 328.831-328.875z" p-id="27509" fill="#d81e06"></path></svg>`)

  /* ---------- 已完成的测距会话（便于统一清除） ---------- */
  const sessions = []   // 每项：{ points, entities }

  /* ---------- 当前会话运行时对象 ---------- */
  let cur = null      // 测距当前会话
  let handler = null // 测距事件处理器
  let isFinishing = false // 防止重复结束会话
  let clickTimeout = null // 用于区分单击和双击

  /* ---------- 空间测距对象 ---------- */
  let spaceCur = null   // 空间测距当前会话
  let spaceHandler = null // 空间测距事件处理器
  const spaceSessions = [] // 空间测距会话列表

  /* ====================================================================== */
  /* 外部 API                                                               */
  /* ====================================================================== */
  function toggleMeasure() {
    isMeasuring.value ? finishSession() : startSession()
  }

  /** 切换空间测距 */
  function toggleSpaceMeasure() {
    isSpaceMeasuring.value ? finishSpaceSession() : startSpaceSession()
  }

  /** 清除所有测距结果 */
  function clearAll() {
    sessions.forEach(s => {
      // 清理实体
      s.entities.forEach(e => viewer.entities.remove(e))
      // 清理事件处理器
      if (s.clearHandler) {
        s.clearHandler.destroy()
      }
    })
    sessions.length = 0

    // 清理空间测距
    spaceSessions.forEach(s => {
      s.entities.forEach(e => viewer.entities.remove(e))
      if (s.clearHandler) {
        s.clearHandler.destroy()
      }
    })
    spaceSessions.length = 0
  }

  /* ====================================================================== */
  /* 会话生命周期                                                            */
  /* ====================================================================== */
  function startSession() {
    if (!viewer) return
    finishSpaceSession()
    // 关掉 Cesium 默认高亮
    viewer.selectedEntity = undefined
    if (viewer.selectionIndicator)
      viewer.selectionIndicator.viewModel.isActive = false

    isMeasuring.value = true
    containerRef.value.style.cursor = 'crosshair'

    // 初始化会话
    cur = {
      points: [],
      entities: [],
      polyline: null,
      rubber: null,
      previewLabel: null,
      clearIcon: null
    }

    // 绑定鼠标事件
    handler?.destroy()
    handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas)

    // 单击：加折返点（延时处理，避免双击时重复触发）
    handler.setInputAction(evt => {
      if (isFinishing) return // 如果正在结束会话，忽略点击

      // 清除之前的延时
      if (clickTimeout) {
        clearTimeout(clickTimeout)
        clickTimeout = null
      }

      // 延时执行，如果在延时期间发生双击，则会被取消
      clickTimeout = setTimeout(() => {
        const cart = pickCartesian(evt.position)
        cart && addPoint(cart)
        clickTimeout = null
      }, 200) // 200ms 延时
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

    // 鼠标移动：橡皮筋 + 预览段长
    handler.setInputAction(evt => {
      if (!cur.points.length) return
      const cart = pickCartesian(evt.endPosition)
      cart && updateRubber(cart)
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

    // 双击：结束（优先级高于单击）
    handler.setInputAction(evt => {
      // 取消单击的延时执行
      if (clickTimeout) {
        clearTimeout(clickTimeout)
        clickTimeout = null
      }
      finishSession()
    }, Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)

    // 右键：结束
    handler.setInputAction(finishSession, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
  }

  function finishSession() {
    if (!isMeasuring.value || isFinishing) return
    isFinishing = true // 防止重复调用

    // 清理点击延时
    if (clickTimeout) {
      clearTimeout(clickTimeout)
      clickTimeout = null
    }

    isMeasuring.value = false
    containerRef.value.style.cursor = ''
    handler?.destroy(); handler = null

    // 删除预览橡皮筋
    cur.rubber && viewer.entities.remove(cur.rubber)
    cur.previewLabel && viewer.entities.remove(cur.previewLabel)

    if (cur.points.length > 1) {
      addClearIcon()                    // clearIcon 内包含“✖ + 总长”
      sessions.push(cur)                // 保存会话
    } else {                            // 只点了一个点 => 直接移除
      cur.entities.forEach(e => viewer.entities.remove(e))
    }
    cur = null                          // 清除引用
    isFinishing = false                 // 重置标志
  }

  /* ====================================================================== */
  /* 会话内部：添加点 / 橡皮筋 / 清除按钮                                    */
  /* ====================================================================== */
  function addPoint(cart) {
    const pts = cur.points   // 局部引用，供 polyline Callback 捕获
    const idx = pts.push(cart) - 1

    /* 1. 红色点 */
    cur.entities.push(
      viewer.entities.add({
        position: cart,
        point: {
          pixelSize: 8,
          color: Cesium.Color.RED,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        }
      })
    )

    /* 2. 点标签（起点 或 累计距离） */
    const labelText = idx === 0 ? '起点' : formatDist(calcTotal(pts))
    const pointLabelEntity = viewer.entities.add({
      position: cart,
      label: pointLabel(labelText)
    })
    cur.entities.push(pointLabelEntity)

    // 保存最后一个点的标签引用，以便后续可能需要移除
    if (idx > 0) {
      cur.lastPointLabel = pointLabelEntity
    }

    /* 3. 折线（首点时创建） */
    if (idx === 0) {
      cur.polyline = viewer.entities.add({
        polyline: {
          positions: new Cesium.CallbackProperty(() => pts, false),
          width: 3,
          material: Cesium.Color.ORANGE,
          clampToGround: true
        }
      })
      cur.entities.push(cur.polyline)
    } else {
      // 段长标签
      addSegmentLabel(pts[idx - 1], pts[idx])
    }
  }

  /* 段长标签放在两点中点 */
  function addSegmentLabel(p0, p1) {
    const mid = Cesium.Cartesian3.midpoint(p0, p1, new Cesium.Cartesian3())
    const len = Cesium.Cartesian3.distance(p0, p1)
    cur.entities.push(
      viewer.entities.add({
        position: mid,
        label: segmentLabel(formatDist(len))
      })
    )
  }

  /* 橡皮筋 + 段长预览 */
  function updateRubber(cart) {
    const last = cur.points.at(-1)

    if (!cur.rubber) {
      cur.rubber = viewer.entities.add({
        polyline: {
          positions: [last, cart],
          width: 2,
          material: new Cesium.PolylineDashMaterialProperty({
            color: Cesium.Color.YELLOW,
            dashLength: 8
          }),
          clampToGround: true
        }
      })
      cur.entities.push(cur.rubber)
    } else {
      cur.rubber.polyline.positions = [last, cart]
    }

    const mid = Cesium.Cartesian3.midpoint(last, cart, new Cesium.Cartesian3())
    const len = Cesium.Cartesian3.distance(last, cart)

    if (!cur.previewLabel) {
      cur.previewLabel = viewer.entities.add({
        position: mid,
        label: segmentLabel(formatDist(len))
      })
      cur.entities.push(cur.previewLabel)
    } else {
      cur.previewLabel.position = mid
      cur.previewLabel.label.text = formatDist(len)
    }
  }

  /* 终点清除按钮（✖ + 总长） */
  function addClearIcon() {
    const last = cur.points.at(-1)
    const total = formatDist(calcTotal(cur.points))
    const eps = 4           // 图标和文字间隙
    const iconSz = 20          // 图标尺寸
    const currentSession = cur // 保存当前会话的引用，避免 cur 被置为 null 后无法访问

    // 移除最后一个点的距离标签，避免与清除按钮的总距离重复
    if (currentSession.lastPointLabel) {
      viewer.entities.remove(currentSession.lastPointLabel)
      const labelIndex = currentSession.entities.indexOf(currentSession.lastPointLabel)
      if (labelIndex > -1) {
        currentSession.entities.splice(labelIndex, 1)
      }
    }

    /* 1️⃣ billboard 显示图标（可拾取） */
    const iconBillboard = viewer.entities.add({
      position: last,
      billboard: {
        image: CLOSE_ICON_URI,
        scale: 0.8,                    // 放大/缩小
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        width: iconSz, height: iconSz,
        pixelOffset: new Cesium.Cartesian2(iconSz / 2 + total.length * eps, -iconSz / 2 + eps),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    cur.entities.push(iconBillboard)

    /* 2️⃣ label 显示总长 */
    const totalLabel = viewer.entities.add({
      position: last,
      label: {
        text: total,
        font: '12px sans-serif',
        fillColor: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK, outlineWidth: 2,
        backgroundColor: Cesium.Color.fromCssColorString('rgba(0,0,0,0.7)'),
        backgroundPadding: new Cesium.Cartesian2(6, 4),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(iconSz / 2, -iconSz / 2),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })
    cur.entities.push(totalLabel)

    // 将清除图标添加到当前会话
    currentSession.clearIcon = iconBillboard
    currentSession.entities.push(iconBillboard)

    /* 悬停改指针 + 点击删除本会话 */
    const tmp = new Cesium.ScreenSpaceEventHandler(viewer.canvas)

    tmp.setInputAction(mv => {
      const p = viewer.scene.pick(mv.endPosition)
      if (p && p.id === iconBillboard) {
        viewer.canvas.style.cursor = 'pointer'
      } else {
        viewer.canvas.style.cursor = ''
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

    tmp.setInputAction(clk => {
      const p = viewer.scene.pick(clk.position)
      if (p && p.id === iconBillboard) {
        // 删除整个会话
        currentSession.entities.forEach(e => viewer.entities.remove(e))
        const sessionIndex = sessions.indexOf(currentSession)
        if (sessionIndex > -1) {
          sessions.splice(sessionIndex, 1)
        }
        viewer.canvas.style.cursor = ''
        tmp.destroy()
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

    // 将事件处理器保存到会话中，以便后续清理
    currentSession.clearHandler = tmp
  }

  /* ====================================================================== */
  /* 工具函数                                                                */
  /* ====================================================================== */
  function pickCartesian(scr) {
    return viewer.scene.pickPosition(scr) ||
      viewer.camera.pickEllipsoid(scr, viewer.scene.globe.ellipsoid)
  }
  const calcTotal = arr => arr.reduce((s, _, i) =>
    i ? s + Cesium.Cartesian3.distance(arr[i - 1], arr[i]) : 0, 0)
  const formatDist = d => d >= 1000 ? (d / 1000).toFixed(2) + ' km' : Math.round(d) + ' m'

  /* label 样式封装 */
  const pointLabel = text => ({
    text, font: '12px Microsoft YaHei',
    fillColor: Cesium.Color.YELLOW,
    outlineColor: Cesium.Color.WHITE, outlineWidth: 2,
    pixelOffset: new Cesium.Cartesian2(0, -25),
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
  })
  const segmentLabel = text => ({
    text, font: '11px sans-serif',
    fillColor: Cesium.Color.YELLOW,
    outlineColor: Cesium.Color.WHITE, outlineWidth: 2,
    backgroundColor: Cesium.Color.fromCssColorString('rgba(0,0,0,0.6)'),
    backgroundPadding: new Cesium.Cartesian2(4, 2),
    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
  })

  /* ====================================================================== */
  /* 空间测距实现                                                            */
  /* ====================================================================== */

  /** 开始空间测距会话 */
  function startSpaceSession() {
    if (!viewer) return

    finishSession()
    // 关掉 Cesium 默认高亮
    viewer.selectedEntity = undefined
    if (viewer.selectionIndicator)
      viewer.selectionIndicator.viewModel.isActive = false

    isSpaceMeasuring.value = true
    containerRef.value.style.cursor = 'crosshair'

    // 初始化空间测距会话
    spaceCur = {
      points: [],
      entities: [],
      line: null,
      clearHandler: null
    }

    // 绑定鼠标事件
    spaceHandler?.destroy()
    spaceHandler = new Cesium.ScreenSpaceEventHandler(viewer.canvas)

    // 单击：添加测距点（空间测距只能点击特定类型的实体）
    spaceHandler.setInputAction(evt => {
      // 首先尝试拾取实体
      const pickedObject = viewer.scene.pick(evt.position)

      if (!pickedObject || !pickedObject.id) {
        // 如果没有点击到实体，显示提示
        showEntityHint(evt.position, '请点击实体（飞机、塔台等）')
        return
      }

      // 检查实体类型，过滤掉不合适的实体
      const entity = pickedObject.id
      if (!isValidSpaceMeasureEntity(entity)) {
        // 如果是线段或其他不合适的实体，显示提示
        showEntityHint(evt.position, '请点击点状实体，不支持线段')
        return
      }

      // 获取实体的位置
      const position = entity.position

      if (!position) {
        console.warn('实体没有位置信息')
        return
      }

      // 获取实体的笛卡尔坐标
      let cart
      if (typeof position.getValue === 'function') {
        cart = position.getValue(viewer.clock.currentTime)
      } else {
        cart = position
      }

      if (!cart) return

      // 使用新的高度计算逻辑：相对于测控车高度
      // 注意：这里需要从主组件获取测控车高度，暂时使用原有逻辑
      if (entity.model && entity.model.heightReference) {
        const heightRef = entity.model.heightReference.getValue ?
          entity.model.heightReference.getValue() : entity.model.heightReference

        if (heightRef === Cesium.HeightReference.CLAMP_TO_GROUND) {
          // 地面实体：保持原有逻辑，因为测控车高度在主组件中
          const cartographic = Cesium.Cartographic.fromCartesian(cart)
          const terrainHeight = viewer.scene.globe.getHeight(cartographic) || 0
          cart = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            terrainHeight
          )
        }
      }

      addSpacePoint(cart, entity)

      // 如果已经有两个点，自动结束测距
      if (spaceCur.points.length === 2) {
        finishSpaceSession()
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

    // 鼠标移动：高亮可点击的实体
    spaceHandler.setInputAction(evt => {
      const pickedObject = viewer.scene.pick(evt.endPosition)

      if (pickedObject && pickedObject.id && isValidSpaceMeasureEntity(pickedObject.id)) {
        // 有效实体，显示可点击状态
        containerRef.value.style.cursor = 'pointer'
        viewer.selectedEntity = pickedObject.id
      } else {
        // 没有实体或无效实体，显示禁止状态
        containerRef.value.style.cursor = 'not-allowed'
        viewer.selectedEntity = undefined
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

    // 右键：取消空间测距
    spaceHandler.setInputAction(() => {
      finishSpaceSession()
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
  }

  /** 结束空间测距会话 */
  function finishSpaceSession() {
    if (!isSpaceMeasuring.value) return

    isSpaceMeasuring.value = false
    containerRef.value.style.cursor = ''
    spaceHandler?.destroy()
    spaceHandler = null

    // 清除实体选中状态
    viewer.selectedEntity = undefined

    if (spaceCur && spaceCur.points.length === 2) {
      // 添加清除按钮
      addSpaceClearIcon()
      // 保存会话
      spaceSessions.push(spaceCur)
    } else if (spaceCur) {
      // 清理未完成的测距
      spaceCur.entities.forEach(e => viewer.entities.remove(e))
    }

    spaceCur = null
  }

  /** 检查实体是否适合空间测距 */
  function isValidSpaceMeasureEntity(entity) {
    // 排除线段类型的实体
    if (entity.polyline) {
      return false // 有polyline属性的是线段
    }

    // 排除多边形实体
    if (entity.polygon) {
      return false // 有polygon属性的是多边形
    }

    // 排除测距相关的实体（避免点击测距线）
    if (entity.label && entity.label.text) {
      const text = typeof entity.label.text === 'string' ? entity.label.text : entity.label.text.getValue()
      if (text && (text.includes('距离') || text.includes('空间') || text.includes('✖'))) {
        return false // 测距相关的标签
      }
    }

    // 只允许有position且有billboard或point或model的实体
    if (entity.position && (entity.billboard || entity.point || entity.model)) {
      return true
    }

    return false
  }

  /** 显示实体选择提示 */
  function showEntityHint(screenPosition, message = '请点击实体（飞机、塔台等）') {
    // 创建临时提示标签
    const worldPosition = viewer.camera.pickEllipsoid(screenPosition, viewer.scene.globe.ellipsoid)
    if (!worldPosition) return

    const hintEntity = viewer.entities.add({
      position: worldPosition,
      label: {
        text: message,
        font: '14px sans-serif',
        fillColor: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.RED,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      }
    })

    // 2秒后自动移除提示
    setTimeout(() => {
      viewer.entities.remove(hintEntity)
    }, 2000)
  }

  /** 添加空间测距点 */
  function addSpacePoint(cart, entity) {
    // 获取实体的实际渲染位置（考虑地形高度）
    let actualPosition = cart

    // 使用实体当前的实际渲染位置
    // 对于使用新高度系统的实体，直接使用其当前位置
    if (entity.model && entity.model.heightReference) {
      const heightRef = entity.model.heightReference.getValue ?
        entity.model.heightReference.getValue() : entity.model.heightReference

      // 无论是相对地面还是贴地，都直接使用实体的当前位置
      // 因为实体位置已经按照新的高度系统计算过了
      actualPosition = cart
    }

    const idx = spaceCur.points.push(actualPosition) - 1

    // 添加测距点（紫色，区别于平面测距）
    // 使用与实体相同的高度参考系统
    const heightReference = entity.model?.heightReference || Cesium.HeightReference.NONE
    const pointEntity = viewer.entities.add({
      position: actualPosition,
      point: {
        pixelSize: 10,
        color: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        heightReference: heightReference // 使用与实体相同的高度参考
      }
    })
    spaceCur.entities.push(pointEntity)

    // 添加点标签（显示实体名称）
    const labelText = idx === 0 ? `起点` : `终点`
    const labelEntity = viewer.entities.add({
      position: actualPosition,
      label: {
        text: labelText,
        font: '12px Microsoft YaHei',
        fillColor: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(0, -30),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        heightReference: heightReference // 使用与实体相同的高度参考
      }
    })
    spaceCur.entities.push(labelEntity)

    // 如果是第二个点，创建连接线和距离标签
    if (idx === 1) {
      createSpaceLine()
    }
  }

  /** 创建空间连接线和距离标签 */
  function createSpaceLine() {
    const [p1, p2] = spaceCur.points

    // 创建3D空间连接线（紫色虚线）
    const lineEntity = viewer.entities.add({
      polyline: {
        positions: [p1, p2],
        width: 3,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
          dashLength: 10
        }),
        clampToGround: false // 不贴地，保持真实3D空间线
      }
    })
    spaceCur.entities.push(lineEntity)
    spaceCur.line = lineEntity

    // 计算3D空间距离
    const distance = Cesium.Cartesian3.distance(p1, p2)

    // 在中点显示距离标签
    const midpoint = Cesium.Cartesian3.midpoint(p1, p2, new Cesium.Cartesian3())
    const distanceEntity = viewer.entities.add({
      position: midpoint,
      label: {
        text: `空间距离: ${formatDist(distance)}`,
        font: '14px Microsoft YaHei',
        fillColor: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        outlineColor: Cesium.Color.WHITE,
        outlineWidth: 2,
        backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        backgroundPadding: new Cesium.Cartesian2(6, 10),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        heightReference: Cesium.HeightReference.NONE
      }
    })
    spaceCur.entities.push(distanceEntity)
  }

  /** 添加空间测距清除按钮 */
  function addSpaceClearIcon() {
    const last = spaceCur.points.at(-1)
    const distance = Cesium.Cartesian3.distance(spaceCur.points[0], spaceCur.points[1])
    const total = formatDist(distance)
    const iconSz = 20, eps = 4
    const currentSession = spaceCur

    // 创建综合 Entity（billboard + label）
    const clearIcon = viewer.entities.add({
      position: last,
      billboard: {
        image: CLOSE_ICON_URI,
        width: iconSz,
        height: iconSz,
        pixelOffset: new Cesium.Cartesian2(iconSz / 2 + total.length * eps * 2, 0),
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        heightReference: Cesium.HeightReference.NONE
      },
      label: {
        text: total,
        font: '12px sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        outlineWidth: 2,
        backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 87, 255)'),
        backgroundPadding: new Cesium.Cartesian2(6, 4),
        pixelOffset: new Cesium.Cartesian2(iconSz / 2, 0),
        horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        heightReference: Cesium.HeightReference.NONE
      }
    })

    currentSession.clearIcon = clearIcon
    currentSession.entities.push(clearIcon)

    // 清除按钮事件处理
    const tmp = new Cesium.ScreenSpaceEventHandler(viewer.canvas)

    tmp.setInputAction(mv => {
      const p = viewer.scene.pick(mv.endPosition)
      if (p && p.id === clearIcon) {
        viewer.canvas.style.cursor = 'pointer'
      } else {
        viewer.canvas.style.cursor = ''
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)

    tmp.setInputAction(clk => {
      const p = viewer.scene.pick(clk.position)
      if (p && p.id === clearIcon) {
        // 删除整个空间测距会话
        currentSession.entities.forEach(e => viewer.entities.remove(e))
        const sessionIndex = spaceSessions.indexOf(currentSession)
        if (sessionIndex > -1) {
          spaceSessions.splice(sessionIndex, 1)
        }
        viewer.canvas.style.cursor = ''
        tmp.destroy()
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

    currentSession.clearHandler = tmp
  }

  /* 组件卸载时清空 */
  onBeforeUnmount(clearAll)

  return {
    isMeasuring,
    isSpaceMeasuring,
    toggleMeasure,
    toggleSpaceMeasure,
    clearMeasure: clearAll // 导出为 clearMeasure 以匹配组件中的调用
  }
}
