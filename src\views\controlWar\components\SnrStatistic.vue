<template>
  <div
    v-resize="{
      handles: ['e', 's', 'se'],
      showHandles: true,
      minWidth: 300,
      minHeight: 200,
      maxWidth: 1200,
      maxHeight: 600,
      draggable: true,
      onResize: handleResize
    }"
    class="snr-statistic"
  >
    <div class="flex items-center gap-x-4 mb-4">
      <el-select v-model="selectedRadio" placeholder="请选择链路" style="width: 300px">
        <el-option
          v-for="radio in radioList"
          :key="radio.value"
          :label="radio.label"
          :value="radio.value"
        />
      </el-select>
      <el-button :disabled="isLoading" type="primary" @click="loopToGetSnr">查询</el-button>
      <el-button :disabled="!isLoading" type="primary" @click="stopLoop">停止</el-button>
    </div>
    <!-- 图表容器 -->
    <div id="snrChart" style="height: calc(100% - 56px)"></div>
  </div>
</template>

<script setup>
  import controlApi from '@/api/spectrumMng/controlWar.js'
  import { ElMessage } from 'element-plus'
  import * as echarts from 'echarts'

  const props = defineProps({
    warTaskId: {
      type: String,
      default: ''
    }
  })

  let timer = null
  let chartInstance = null
  const isLoading = ref(false)
  const selectedRadio = ref(null)
  const radioList = ref([
    { label: '电台B2_无人机B号-电台B1', value: '8_7' },
    { label: '电台A2 无人机A号-电台A1', value: '2_1' },
    { label: '电台D2 无人机D号-电台D1', value: '6_5' },
    { label: '电台c2_无人机c号-电台C1', value: '10_9' }
  ])

  /**
   * @description: 获取无线电列表
   */
  const getRadioList = async () => {
    const res = await controlApi.queryRadioLink({ warTaskId: props.warTaskId })
    if (res.code === 200) {
      radioList.value = res.data
    }
  }

  /**
   * @description: 初始化图表
   */
  const initChart = () => {
    const chartDom = document.getElementById('snrChart')
    chartInstance = echarts.init(chartDom)

    const option = {
      title: {
        text: '信噪比统计',
        textStyle: { color: '#fff' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = `时间: ${params[0].axisValue}<br/>`
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value}<br/>`
          })
          return result
        }
      },
      legend: {
        data: [], // 初始化为空，在 updateChart 时动态设置
        textStyle: { color: '#fff' }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#fff' } }
      },
      yAxis: {
        type: 'value',
        name: '信噪比',
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#fff' } },
        splitLine: { lineStyle: { color: '#c1c1c1' } },
        min: -10,
        max: 33,
        interval: 5
      },
      series: []
    }

    chartInstance.setOption(option)
  }

  /**
   * @description: 更新图表数据
   */
  const updateChart = data => {
    if (!chartInstance || !data?.dateList) return
    const xAxisData = data.dateList
    const sourceData = (data.sourceSnrs || []).map(d => ({ snr: +d.snr, remark: d.remark }))
    const targetData = (data.targetSnrs || []).map(d => ({ snr: +d.snr, remark: d.remark }))

    // ① 每个点都可单独配置 label
    const sourceSeriesData = sourceData.map(i => {
      const item = { value: i.snr }
      if (i.remark) {
        // 异常点：把数值标签放在下面或加偏移
        item.label = { show: true, position: 'bottom', offset: [0, 8], color: '#409EFF' }
      }
      return item
    })
    const targetSeriesData = targetData.map(i => {
      const item = { value: i.snr }
      if (i.remark) {
        item.label = { show: true, position: 'bottom', offset: [0, 8], color: '#67C23A' }
      }
      return item
    })

    // ② markPoint 往上挪一点，避免和“top”位置冲突
    const sourceMarkPoints = []
    const targetMarkPoints = []
    sourceData.forEach((item, index) => {
      if (item.remark) {
        sourceMarkPoints.push({
          coord: [xAxisData[index], item.snr],
          name: item.remark,
          value: item.snr,
          itemStyle: { color: '#ff4757' }
        })
      }
    })
    targetData.forEach((item, index) => {
      if (item.remark) {
        targetMarkPoints.push({
          coord: [xAxisData[index], item.snr],
          name: item.remark,
          value: item.snr,
          itemStyle: { color: '#ff4757' }
        })
      }
    })

    chartInstance.setOption({
      xAxis: { data: xAxisData },
      legend: {
        data: [data.sourceName || 'Source', data.targetName || 'Target']
      },
      series: [
        {
          name: data.sourceName || 'Source',
          type: 'line',
          data: sourceSeriesData,
          itemStyle: { color: '#409EFF' },
          label: { show: true, position: 'top', color: '#409EFF' },
          labelLayout: { moveOverlap: 'shiftY' },
          markPoint: {
            data: sourceMarkPoints,
            symbol: 'pin',
            symbolSize: 50,
            symbolOffset: [0, -6],
            z: 10,
            label: { show: true, formatter: '{b}', color: '#fff', offset: [0, -6] }
          }
        },
        {
          name: data.targetName || 'Target',
          type: 'line',
          data: targetSeriesData,
          itemStyle: { color: '#67C23A' },
          label: { show: true, position: 'top', color: '#67C23A' },
          labelLayout: { moveOverlap: 'shiftY' },
          markPoint: {
            data: targetMarkPoints,
            symbol: 'pin',
            symbolSize: 50,
            symbolOffset: [0, -6],
            z: 10,
            label: { show: true, formatter: '{b}', color: '#fff', offset: [0, -6] }
          }
        }
      ]
    })
  }

  /**
   * @description: 循环获取信噪比统计
   */
  const loopToGetSnr = async () => {
    if (!selectedRadio.value) {
      ElMessage.error('请选择无线电')
      return
    }
    if (timer) {
      clearInterval(timer)
    }

    // 初始化图表
    if (!chartInstance) {
      await nextTick()
      initChart()
    }

    timer = setInterval(async () => {
      const res = await controlApi.querySnrStatistics({
        optionModelId: selectedRadio.value
      })
      if (res.code === 200) {
        updateChart(res.data)
      }
    }, 1000)
  }

  // const loopToGetSnr = async () => {
  //   if (!selectedRadio.value) {
  //     ElMessage.error('请选择链路')
  //     return
  //   }
  //   if (timer) {
  //     clearInterval(timer)
  //   }

  //   // 初始化图表
  //   if (!chartInstance) {
  //     await nextTick()
  //     initChart()
  //   }
  //   isLoading.value = true
  //   timer = setInterval(() => {
  //     // 模拟返回数据
  //     const now = new Date()
  //     const fakeRes = {
  //       code: 200,
  //       data: {
  //         targetName: '无人机B号-电台B1',
  //         sourceName: '电台B2',
  //         dateList: [
  //           new Date(now.getTime() - 7200000).toISOString(), // 2小时前
  //           new Date(now.getTime() - 3600000).toISOString(), // 1小时前
  //           now.toISOString() // 当前时间
  //         ],
  //         sourceSnrs: [
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1), // -10 ~ 10 的随机值
  //             remark: '异常'
  //           },
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1),
  //             remark: null
  //           },
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1),
  //             remark: null
  //           }
  //         ],
  //         targetSnrs: [
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1),
  //             remark: null
  //           },
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1),
  //             remark: null
  //           },
  //           {
  //             snr: (Math.random() * 20 - 10).toFixed(1),
  //             remark: null
  //           }
  //         ]
  //       }
  //     }
  //     updateChart(fakeRes.data)
  //   }, 1000)
  // }

  const stopLoop = () => {
    if (timer) {
      clearInterval(timer)
    }
    isLoading.value = false
  }

  onMounted(async () => {
    await getRadioList()
  })

  onBeforeUnmount(() => {
    if (timer) {
      clearInterval(timer)
    }
    if (chartInstance) {
      chartInstance.dispose()
    }
  })

  // resize 回调函数
  const handleResize = () => {
    if (chartInstance) {
      nextTick(() => {
        chartInstance.resize()
      })
    }
  }
</script>

<style lang="scss" scoped>
  .snr-statistic {
    min-width: 300px;
    min-height: 200px;
    background: rgba(0, 0, 0, 0.65);
    opacity: 1 !important; /* 强制设置不透明 */
    padding: 10px;
    z-index: 1000;
  }
</style>
