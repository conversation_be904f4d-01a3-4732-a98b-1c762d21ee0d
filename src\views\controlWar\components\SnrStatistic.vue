<template>
  <div
    v-resize="{
      handles: ['e', 's', 'se'],
      showHandles: true,
      minWidth: 300,
      minHeight: 200,
      maxWidth: 1200,
      maxHeight: 600,
      draggable: true,
      onResize: handleResize
    }"
    class="snr-statistic"
  >
    <div class="flex items-center gap-x-4 mb-4">
      <el-select v-model="selectedRadio" placeholder="请选择链路" style="width: 300px">
        <el-option
          v-for="radio in radioList"
          :key="radio.value"
          :label="radio.label"
          :value="radio.value"
        />
      </el-select>
      <el-button :disabled="isLoading" type="primary" @click="loopToGetSnr">查询</el-button>
      <el-button :disabled="!isLoading" type="primary" @click="stopLoop">停止</el-button>
    </div>
    <!-- 图表容器 -->
    <div id="snrChart" style="height: calc(100% - 56px)"></div>
  </div>
</template>

<script setup>
  import controlApi from '@/api/spectrumMng/controlWar.js'
  import { ElMessage } from 'element-plus'
  import * as echarts from 'echarts'

  const props = defineProps({
    warTaskId: {
      type: String,
      default: ''
    }
  })

  let timer = null
  let chartInstance = null
  const isLoading = ref(false)
  const selectedRadio = ref(null)
  const radioList = ref([
    { label: '电台B2_无人机B号-电台B1', value: '8_7' },
    { label: '电台A2 无人机A号-电台A1', value: '2_1' },
    { label: '电台D2 无人机D号-电台D1', value: '6_5' },
    { label: '电台c2_无人机c号-电台C1', value: '10_9' }
  ])

  /**
   * @description: 获取无线电列表
   */
  const getRadioList = async () => {
    const res = await controlApi.queryRadioLink({ warTaskId: props.warTaskId })
    if (res.code === 200) {
      radioList.value = res.data
    }
  }

  /**
   * @description: 初始化图表
   */
  const initChart = () => {
    const chartDom = document.getElementById('snrChart')
    chartInstance = echarts.init(chartDom)

    const option = {
      title: {
        text: '信噪比统计',
        textStyle: { color: '#fff' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params) {
          let result = `时间: ${params[0].axisValue}<br/>`
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value}<br/>`
          })
          return result
        }
      },
      legend: {
        data: [], // 初始化为空，在 updateChart 时动态设置
        textStyle: { color: '#fff' }
      },
      // 添加数据缩放组件，支持X轴拖拽
      dataZoom: [
        {
          type: 'inside', // 内置型数据区域缩放组件
          xAxisIndex: 0,
          filterMode: 'none'
        },
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          xAxisIndex: 0,
          bottom: 10,
          height: 20,
          handleIcon:
            'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
          handleSize: '80%',
          handleStyle: {
            color: '#fff',
            shadowBlur: 3,
            shadowColor: 'rgba(0, 0, 0, 0.6)',
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          textStyle: {
            color: '#fff'
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%', // 为滑动条留出空间
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: '#fff',
          rotate: 45 // 旋转标签避免重叠
        },
        axisLine: { lineStyle: { color: '#fff' } }
      },
      yAxis: {
        type: 'value',
        name: '信噪比',
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#fff' } },
        splitLine: { lineStyle: { color: '#c1c1c1' } },
        min: -10,
        max: 33,
        interval: 5
      },
      series: []
    }

    chartInstance.setOption(option)
  }

  /**
   * @description: 更新图表数据
   */
  const updateChart = data => {
    if (!chartInstance || !data?.dateList) return
    const xAxisData = data.dateList
    const sourceData = (data.sourceSnrs || []).map(d => ({ snr: +d.snr, remark: d.remark }))
    const targetData = (data.targetSnrs || []).map(d => ({ snr: +d.snr, remark: d.remark }))

    // ① 每个点都可单独配置 label
    const sourceSeriesData = sourceData.map(i => {
      const item = { value: i.snr }
      if (i.remark) {
        // 异常点：把数值标签放在下面或加偏移
        item.label = { show: true, position: 'bottom', offset: [0, 8], color: '#409EFF' }
      }
      return item
    })
    const targetSeriesData = targetData.map(i => {
      const item = { value: i.snr }
      if (i.remark) {
        item.label = { show: true, position: 'bottom', offset: [0, 8], color: '#67C23A' }
      }
      return item
    })

    // ② markPoint 往上挪一点，避免和“top”位置冲突
    const sourceMarkPoints = []
    const targetMarkPoints = []
    sourceData.forEach((item, index) => {
      if (item.remark) {
        sourceMarkPoints.push({
          coord: [xAxisData[index], item.snr],
          name: item.remark,
          value: item.snr,
          itemStyle: { color: '#ff4757' }
        })
      }
    })
    targetData.forEach((item, index) => {
      if (item.remark) {
        targetMarkPoints.push({
          coord: [xAxisData[index], item.snr],
          name: item.remark,
          value: item.snr,
          itemStyle: { color: '#ff4757' }
        })
      }
    })

    // 构建series数组
    const series = [
      {
        name: data.sourceName || 'Source',
        type: 'line',
        data: sourceSeriesData,
        itemStyle: { color: '#409EFF' },
        label: { show: true, position: 'top', color: '#409EFF' },
        labelLayout: { moveOverlap: 'shiftY' },
        markPoint: {
          data: sourceMarkPoints,
          symbol: 'pin',
          symbolSize: 50,
          symbolOffset: [0, -6],
          z: 10,
          label: { show: true, formatter: '{b}', color: '#fff', offset: [0, -6] }
        }
      },
      {
        name: data.targetName || 'Target',
        type: 'line',
        data: targetSeriesData,
        itemStyle: { color: '#67C23A' },
        label: { show: true, position: 'top', color: '#67C23A' },
        labelLayout: { moveOverlap: 'shiftY' },
        markPoint: {
          data: targetMarkPoints,
          symbol: 'pin',
          symbolSize: 50,
          symbolOffset: [0, -6],
          z: 10,
          label: { show: true, formatter: '{b}', color: '#fff', offset: [0, -6] }
        }
      }
    ]

    // 添加信噪比阈值线
    if (data.snrThreshold !== undefined && data.snrThreshold !== null) {
      const thresholdData = xAxisData.map(() => data.snrThreshold)
      series.push({
        name: '信噪比阈值',
        type: 'line',
        data: thresholdData,
        lineStyle: {
          color: '#ff0000',
          type: 'dashed',
          width: 2
        },
        itemStyle: { color: '#ff0000' },
        symbol: 'none',
        z: 5
      })
    }

    chartInstance.setOption({
      xAxis: { data: xAxisData },
      legend: {
        data: [data.sourceName || 'Source', data.targetName || 'Target', '信噪比阈值']
      },
      series: series
    })
  }

  /**
   * @description: 循环获取信噪比统计
   */
  const loopToGetSnr = async () => {
    if (!selectedRadio.value) {
      ElMessage.error('请选择无线电')
      return
    }
    if (timer) {
      clearInterval(timer)
    }

    // 初始化图表
    if (!chartInstance) {
      await nextTick()
      initChart()
    }
    isLoading.value = true
    timer = setInterval(async () => {
      const res = await controlApi.querySnrStatistics({
        optionModelId: selectedRadio.value
      })
      if (res.code === 200) {
        updateChart(res.data)
      }
    }, 1000)
  }

  const stopLoop = () => {
    if (timer) {
      clearInterval(timer)
    }
    isLoading.value = false
  }

  onMounted(async () => {
    await getRadioList()
  })

  onBeforeUnmount(() => {
    if (timer) {
      clearInterval(timer)
    }
    if (chartInstance) {
      chartInstance.dispose()
    }
  })

  // resize 回调函数
  const handleResize = () => {
    if (chartInstance) {
      nextTick(() => {
        chartInstance.resize()
      })
    }
  }
</script>

<style lang="scss" scoped>
  .snr-statistic {
    width: 300px;
    height: 200px;
    min-width: 300px;
    min-height: 200px;
    background: rgba(0, 0, 0, 0.65);
    opacity: 1 !important; /* 强制设置不透明 */
    padding: 10px;
    z-index: 1000;
  }
</style>
