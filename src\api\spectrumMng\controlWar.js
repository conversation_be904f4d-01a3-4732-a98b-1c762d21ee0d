import request from '@/utils/request'

// 战中管控API集合
const controlApi = {
  // 查询无线电链接
  queryRadioLink: (params) => request({
    url: '/war-ing/queryRadioLink',
    method: 'get',
    params
  }),
  // 查询信噪比统计
  querySnrStatistics: (params) => request({
    url: '/war-ing/querySnrStatistics',
    method: 'get',
    params
  }),
  // 查询战中区域
  queryWarArea: (params) => request({
    url: '/war-ing/queryWarArea',
    method: 'get',
    params
  })
}

export default controlApi
