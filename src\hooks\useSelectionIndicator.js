import { ref } from 'vue'
import * as Cesium from 'cesium'

export function useSelectionIndicator(viewer) {
  const customSelectionIndicator = ref(null)
  const selectedEntity = ref(null)
  const showEntityPanel = ref(false)
  const entityInfo = ref({})

  // 创建选择指示器的Canvas图像
  function createSelectionIndicatorCanvas() {
    const canvas = document.createElement('canvas')
    canvas.width = 80
    canvas.height = 80
    const ctx = canvas.getContext('2d')

    // 设置样式
    ctx.strokeStyle = '#00FF00' // 绿色
    ctx.lineWidth = 2
    ctx.lineCap = 'square'

    const size = 80
    const cornerLength = 15 // 角的长度
    const offset = 5 // 距离边缘的偏移

    // 清除画布
    ctx.clearRect(0, 0, size, size)

    // 绘制四个角的方框
    // 左上角
    ctx.beginPath()
    ctx.moveTo(offset, offset + cornerLength)
    ctx.lineTo(offset, offset)
    ctx.lineTo(offset + cornerLength, offset)
    ctx.stroke()

    // 右上角
    ctx.beginPath()
    ctx.moveTo(size - offset - cornerLength, offset)
    ctx.lineTo(size - offset, offset)
    ctx.lineTo(size - offset, offset + cornerLength)
    ctx.stroke()

    // 右下角
    ctx.beginPath()
    ctx.moveTo(size - offset, size - offset - cornerLength)
    ctx.lineTo(size - offset, size - offset)
    ctx.lineTo(size - offset - cornerLength, size - offset)
    ctx.stroke()

    // 左下角
    ctx.beginPath()
    ctx.moveTo(offset + cornerLength, size - offset)
    ctx.lineTo(offset, size - offset)
    ctx.lineTo(offset, size - offset - cornerLength)
    ctx.stroke()

    return canvas.toDataURL()
  }

  // 判断是否为实体类型
  function isEntityType(entity) {
    if (!entity) return false
    return entity.model || (entity.name && (entity.name.includes('无人机') || entity.name.includes('雷达')))
  }

  // 获取实体位置
  function getEntityPosition(entity) {
    try {
      if (entity.position._value) {
        return entity.position._value
      } else if (typeof entity.position.getValue === 'function') {
        return entity.position.getValue(viewer.clock.currentTime)
      } else if (entity.position instanceof Cesium.Cartesian3) {
        return entity.position
      }
    } catch (error) {
      console.warn('获取实体位置失败:', error)
    }
    return null
  }

  // 获取实体信息
  function getEntityInfo(entity) {
    const position = getEntityPosition(entity)
    let coordinates = { lon: 0, lat: 0, alt: 0 }

    if (position) {
      const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(position)
      coordinates = {
        lon: Cesium.Math.toDegrees(cartographic.longitude).toFixed(6),
        lat: Cesium.Math.toDegrees(cartographic.latitude).toFixed(6),
        alt: cartographic.height.toFixed(2)
      }
    }

    return {
      id: entity.id,
      name: entity.name || '未命名实体',
      type: entity.name?.includes('无人机') ? '无人机' : entity.name?.includes('雷达') ? '雷达' : '未知',
      coordinates,
      status: '在线', // 可以根据实际情况动态获取
      lastUpdate: new Date().toLocaleString()
    }
  }

  // 显示自定义选择指示器
  function showSelectionIndicator(entity) {
    if (!viewer || !entity || !isEntityType(entity)) return

    // 先隐藏之前的指示器
    hideSelectionIndicator()

    // 创建新的选择指示器，使用与实体相同的高度参考系统
    customSelectionIndicator.value = viewer.entities.add({
      position: new Cesium.CallbackProperty(() => {
        return getEntityPosition(entity) || Cesium.Cartesian3.ZERO
      }, false),
      billboard: {
        image: createSelectionIndicatorCanvas(),
        width: 80,
        height: 80,
        pixelOffset: new Cesium.Cartesian2(0, 0),
        eyeOffset: new Cesium.Cartesian3(0, 0, -100),
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
        // 使用与实体相同的高度参考系统
        heightReference: entity.model?.heightReference || Cesium.HeightReference.NONE
      }
    })

    // 设置选中实体和信息
    selectedEntity.value = entity
    entityInfo.value = getEntityInfo(entity)
    showEntityPanel.value = true
  }

  // 隐藏自定义选择指示器
  function hideSelectionIndicator() {
    if (customSelectionIndicator.value && viewer) {
      viewer.entities.remove(customSelectionIndicator.value)
      customSelectionIndicator.value = null
    }
    selectedEntity.value = null
    showEntityPanel.value = false
    entityInfo.value = {}
  }

  return {
    selectedEntity,
    showEntityPanel,
    entityInfo,
    isEntityType,
    showSelectionIndicator,
    hideSelectionIndicator,
    getEntityPosition
  }
}