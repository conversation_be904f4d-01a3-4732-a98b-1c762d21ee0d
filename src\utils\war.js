// ================================
// Imports
// ================================
import * as Cesium from 'cesium'
import PolylineWakeLineMaterialProperty from '@/hooks/useDynamicWallMaterialProperty.js'
import verticalDrone from '@/assets/3dModel/Aerial_Explorer_0722142012_texture.glb'
import smallSRDrone from '@/assets/3dModel/Hexacopter_Drone_0722143313_texture.glb'
import bigSRDrone from '@/assets/3dModel/HexaFlyer_0722141250_texture.glb'
import FRDrone from '@/assets/3dModel/Quadcopter_Flight_0722142641_texture.glb'
import Radar from '@/assets/3dModel/Radar.glb'
import signalCorps from '@/assets/3dModel/signalCorps.glb'
import radarVehicle from '@/assets/3dModel/radarVehicle.glb'

import AmapMercatorTilingScheme from './AmapMercatorTilingScheme.js'

/**
 * 安全归一化向量
 * @param {Cesium.Cartesian3} vec 输入向量
 * @param {Cesium.Cartesian3} [out=new Cesium.Cartesian3()] 输出向量
 * @returns {?Cesium.Cartesian3} 归一化后的向量或 null
 */
const EPS = 1e-6;
function safeNormalize(vec, out = new Cesium.Cartesian3()) {
  const len = Cesium.Cartesian3.magnitude(vec);
  if (!Number.isFinite(len) || len < EPS) return null;
  return Cesium.Cartesian3.divideByScalar(vec, len, out);
}
function geodeticUp(cart) {
  // 用椭球面法向，避免 normalize(位置向量) 在特殊几何下出问题
  const out = new Cesium.Cartesian3();
  Cesium.Ellipsoid.WGS84.geodeticSurfaceNormal(cart, out);
  return out;
}


// ================================
// 模型选择 & 缩放
// ================================
let droneModelCounter = 0
// const droneModels = [verticalDrone, smallSRDrone, bigSRDrone, FRDrone]  // 垂起、小六、大六、四旋翼
const droneModels = [bigSRDrone, smallSRDrone, FRDrone, verticalDrone]  // 垂起、小六、大六、四旋翼

/**
 * 根据类型选择对应模型
 * @param {string} model 类型标识
 * @returns {string} 模型文件路径
 */
export const chooseModel = model => {
  switch (model) {
    case '1':
      const sel = droneModels[droneModelCounter % droneModels.length]
      droneModelCounter++
      return sel
    case '2': return signalCorps
    case '3': return radarVehicle
    case '4': return Radar
    case '5': return signalCorps
    default:
      const def = droneModels[droneModelCounter % droneModels.length]
      droneModelCounter++
      return def
  }
}

/**
 * 根据模型类型返回渲染缩放系数
 * @param {string} model 类型标识
 * @returns {number} 缩放系数
 */
export const chooseScale = model => {
  switch (model) {
    case '1': return 5
    case '2': return 0.3
    case '3': return 10
    case '4': return 1
    case '5': return 0.3
    default: return 5
  }
}

// ================================
// Canvas 画布缓存工具
// ================================
const canvasPool = { small: new Map(), big: new Map() }

/**
 * 获取或创建 Canvas 对象，并复用缓存
 * @param {'small'|'big'} type 画布类型
 * @param {number} width 宽度
 * @param {number} height 高度
 * @returns {HTMLCanvasElement}
 */
export const getCanvas = (type, width, height) => {
  const key = `${width}x${height}`
  const pool = canvasPool[type]
  if (pool.has(key)) {
    const c = pool.get(key)
    c.getContext('2d').clearRect(0, 0, width, height)
    return c
  }
  const c = document.createElement('canvas')
  c.width = width; c.height = height
  if (pool.size >= 5) pool.delete(pool.keys().next().value)
  pool.set(key, c)
  return c
}

// ================================
// 树结构处理
// ================================
/**
 * 递归处理接口返回的树结构，统一字段
 * @param {Object} node 原始节点
 * @param {string} warTaskId 任务ID
 * @returns {Object} 处理后节点
 */
export function processTreeNode(node, warTaskId) {
  return {
    id: node.id,
    title: node.title,
    hasChildren: node.hasChild,
    isLeaf: node.type === 'WAR_TASK_DEC_LEAF',
    originalId: node.originalId,
    type: node.type,
    warTaskId,
    children: (node.children || []).map(child => processTreeNode(child, warTaskId))
  }
}

// ================================
// 几何 & 热力图计算
// ================================
/**
 * 计算多个经纬度点的中心位置
 * @param {Array<{lon: number, lat: number, alt?: number}>} points
 * @returns {{lon: number, lat: number, alt: number}|null}
 */
export const calculateCenterPoint = points => {
  if (!points?.length) return null
  const sum = points.reduce((acc, p) => {
    acc.lon += +p.lon; acc.lat += +p.lat; acc.alt += +p.alt || 0; return acc
  }, { lon: 0, lat: 0, alt: 0 })
  const n = points.length
  return { lon: sum.lon / n, lat: sum.lat / n, alt: sum.alt / n || 50000 }
}

/**
 * 按阈值生成平面热力图并拉伸平滑
 * @param {number[][]} pEr 矩阵数据
 * @param {number} nRow 行
 * @param {number} nCol 列
 * @param {number} smoothSize 目标尺寸
 * @returns {HTMLCanvasElement}
 */
export const makeHeatCanvas = (pEr, nRow, nCol, smoothSize = 512) => {
  const small = getCanvas('small', nCol, nRow)
  const sCtx = small.getContext('2d')
  const flat = pEr.flat(), vMin = Math.min(...flat), vMax = Math.max(...flat)
  const thresh = 20
  for (let i = 0; i < nRow; i++) for (let j = 0; j < nCol; j++) {
    const v = pEr[i][j]; if (v < thresh) continue
    const t = (v - vMin) / (vMax - vMin)
    sCtx.fillStyle = Cesium.Color.fromHsl((1 - t) * 0.66, 1, 0.5).withAlpha(0.6).toCssColorString()
    sCtx.fillRect(j, i, 1, 1)
  }
  const big = getCanvas('big', smoothSize, smoothSize)
  const bCtx = big.getContext('2d')
  bCtx.imageSmoothingEnabled = true; bCtx.imageSmoothingQuality = 'high'
  bCtx.drawImage(small, 0, 0, smoothSize, smoothSize)
  return big
}

/**
 * 计算中心点与范围经度差构成的地图半径（米）
 * @param {number} lon
 * @param {number} lat
 * @param {[number, number]} lonlim
 * @returns {{centerLon:number,centerLat:number,radius:number}}
 */
export const calculatePointInfo = (lon, lat, lonlim) => {
  const c0 = Cesium.Cartographic.fromDegrees(lon, lat)
  const ce = Cesium.Cartographic.fromDegrees(lonlim[1], lat)
  return { centerLon: lon, centerLat: lat, radius: new Cesium.EllipsoidGeodesic(c0, ce).surfaceDistance }
}

// ================================
// 动态连线 & 光流材质
// ================================
/**
 * 获取连线中点回调
 * @param {Entity} lineEnt
 */
function makeMidCallback(lineEnt, tracks, sourcePlatformId, targetPlatformId, viewer) {
  let lastValid = null
  return new Cesium.CallbackProperty((time) => {
    const positionsProp = lineEnt.polyline.positions
    const positions = positionsProp && positionsProp.getValue ? positionsProp.getValue(time) : null
    if (!positions || positions.length < 2) {
      // 返回上一次有效值，避免落到地心
      return lastValid ?? Cesium.Cartesian3.fromDegrees(0, 0, -1e6) // 隐到地球内部
    }
    let p1 = positions[0]
    let p2 = positions[1]

    // positions 已是“绝对高程”的点（前面 getPositions 统一过）
    // 求 3D 中点
    const mid = Cesium.Cartesian3.midpoint(p1, p2, new Cesium.Cartesian3())

    // 如果两端都是地面实体：把 label 贴地（略抬 1m，避免和地表深度测试冲突）
    const fromTrack = tracks.get(sourcePlatformId)
    const toTrack = tracks.get(targetPlatformId)
    if (fromTrack?.isGroundEntity && toTrack?.isGroundEntity) {
      const c = Cesium.Cartographic.fromCartesian(mid)
      const terrain = viewer.scene.globe.getHeight(c) || 0
      lastValid = Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, terrain + 1.0)
      return lastValid
    }

    // 否则用 3D 中点
    lastValid = mid
    return mid
  }, false)
}



// 统一：都换成绝对高程坐标
export function toAbsolute(cart, viewer) {
  if (!cart) return null
  const c = Cesium.Cartographic.fromCartesian(cart)
  const terrain = viewer.scene.globe.getHeight(c) || 0
  // 这里的 c.height：如果是无人机，就是“相对地面高度”；如果是地面实体，基本是 0
  const absHeight = terrain + (c.height || 0)
  return Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, absHeight)
}

/**
 * 添加或更新动态尾迹线
 */
export const addDynamicLine = ({
  tracks,
  polylineMap,
  sourcePlatformId,
  targetPlatformId,
  signalStrength,
  viewer,
  jam = false,
  offLine = false,
  offsetMeters = 3,
  defaultColor = Cesium.Color.fromHsl(0.333, 1.0, 0.7),
  labelOffsetPx = 12, // ★ 新增：label 垂直偏移像素
}) => {
  if (!viewer) return null;
  const key = `${sourcePlatformId}_${targetPlatformId}`;
  const reverseKey = `${targetPlatformId}_${sourcePlatformId}`;
  const fromEnt = tracks.get(sourcePlatformId)?.marker;
  const toEnt = tracks.get(targetPlatformId)?.marker;
  if (!fromEnt || !toEnt) {
    console.warn(`无法绘制连线，找不到点 ${sourcePlatformId} 或 ${targetPlatformId}`);
    return null;
  }

  const hasBidirectional = polylineMap[reverseKey] !== undefined;

  // 修正颜色逻辑
  let lineColor;
  if (offLine) {
    lineColor = Cesium.Color.RED; // 离线时总是红色
  } else if (jam) {
    lineColor = Cesium.Color.RED; // 干扰时红色
  } else {
    lineColor = defaultColor; // 正常时绿色
  }

  // --- 材质：记住上一次类型，只在变化时重建 ---
  const materialKeyOf = (flag) => (flag ? 'dash' : 'wake');
  const makeMaterial = () => {
    if (offLine) {
      return new Cesium.PolylineDashMaterialProperty({
        color: lineColor,
        gapColor: Cesium.Color.TRANSPARENT,
        dashLength: 16
      });
    }
    return new PolylineWakeLineMaterialProperty({
      color: lineColor,
      duration: 3000,
      arrowSpacing: 0.20,
      arrowWidth: 0.06,
      dir: -1.0,
      speed: 0.8
    });
  };

  const getPositions = new Cesium.CallbackProperty(time => {
    let p1 = fromEnt.position.getValue(time)
    let p2 = toEnt.position.getValue(time)

    const p1Abs = toAbsolute(p1, viewer)
    const p2Abs = toAbsolute(p2, viewer)
    if (!p1Abs || !p2Abs) return []

    if (!hasBidirectional || offsetMeters === 0) return [p1Abs, p2Abs]

    // 做横向偏移也基于“绝对坐标”
    // const mid = Cesium.Cartesian3.midpoint(p1Abs, p2Abs, new Cesium.Cartesian3())
    // const dir = Cesium.Cartesian3.subtract(p2Abs, p1Abs, new Cesium.Cartesian3())
    // Cesium.Cartesian3.normalize(dir, dir)
    // const up = Cesium.Cartesian3.normalize(mid, new Cesium.Cartesian3())
    // const right = Cesium.Cartesian3.cross(dir, up, new Cesium.Cartesian3())
    // Cesium.Cartesian3.normalize(right, right)
    // const offset = Cesium.Cartesian3.multiplyByScalar(right, offsetMeters, new Cesium.Cartesian3())
    // return [
    //   Cesium.Cartesian3.add(p1Abs, offset, new Cesium.Cartesian3()),
    //   Cesium.Cartesian3.add(p2Abs, offset, new Cesium.Cartesian3())
    // ]
    const mid = Cesium.Cartesian3.midpoint(p1Abs, p2Abs, new Cesium.Cartesian3());

    // dir：两点重合就不偏移
    const dir = safeNormalize(Cesium.Cartesian3.subtract(p2Abs, p1Abs, new Cesium.Cartesian3()));
    if (!dir) return [p1Abs, p2Abs];

    // up：用地表法向而不是 normalize(mid)
    const up = geodeticUp(mid);

    // right：接近 0 就不偏移
    const right = safeNormalize(Cesium.Cartesian3.cross(dir, up, new Cesium.Cartesian3()));
    if (!right) return [p1Abs, p2Abs];

    const offset = Cesium.Cartesian3.multiplyByScalar(right, offsetMeters, new Cesium.Cartesian3());
    return [
      Cesium.Cartesian3.add(p1Abs, offset, new Cesium.Cartesian3()),
      Cesium.Cartesian3.add(p2Abs, offset, new Cesium.Cartesian3())
    ];

  }, false)


  // ====== 已有则更新 ======
  if (polylineMap[key]) {
    const ent = polylineMap[key];
    ent.polyline.positions = getPositions;
    ent.polyline.clampToGround = false;

    // 材质：仅在“类型”变化时替换；否则只更新 color
    const nextKey = materialKeyOf(offLine);
    if (ent.__materialKey !== nextKey) {
      ent.polyline.material = makeMaterial();
      ent.__materialKey = nextKey;
    } else {
      const mat = ent.polyline.material;
      if (mat && 'color' in mat) mat.color = lineColor;
      else { ent.polyline.material = makeMaterial(); ent.__materialKey = nextKey; }
    }

    // ★ Label：更新文本 & 偏移（正向：上；若存在反向线，再把反向线的 label 下移）
    const labelEnt = polylineMap[`${key}_label`];
    if (labelEnt?.label) {
      labelEnt.label.text = signalStrength.toFixed(0);
      labelEnt.label.pixelOffset = new Cesium.Cartesian2(0, hasBidirectional ? -labelOffsetPx : 0);
    }
    if (hasBidirectional) {
      const revLabel = polylineMap[`${reverseKey}_label`];
      if (revLabel?.label) {
        revLabel.label.pixelOffset = new Cesium.Cartesian2(0, labelOffsetPx);
      }
    }
    return ent;
  }

  // ====== 新建 ======
  const lineEnt = viewer.entities.add({
    id: key,
    polyline: {
      positions: getPositions,
      width: offLine ? 3 : 2,
      material: makeMaterial(),
      clampToGround: false
    }
  });
  lineEnt.__materialKey = materialKeyOf(offLine);

  const labelEnt = viewer.entities.add({
    id: `${key}_label`,
    position: makeMidCallback(lineEnt, tracks, sourcePlatformId, targetPlatformId, viewer),
    label: {
      text: signalStrength.toFixed(0),
      font: '18px sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      pixelOffset: new Cesium.Cartesian2(0, hasBidirectional ? -labelOffsetPx : 0),
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      // 位置是“绝对高程”，所以用 NONE；并给点深度测试距离
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: 5000
    }
  })


  polylineMap[key] = lineEnt;
  polylineMap[`${key}_label`] = labelEnt;

  // 若存在反向连线：设置反向线位置与 label 向下偏移
  if (hasBidirectional && polylineMap[reverseKey]) {
    // polylineMap[reverseKey].polyline.positions = new Cesium.CallbackProperty(time => {
    //   const p1 = toEnt.position.getValue(time);
    //   const p2 = fromEnt.position.getValue(time);

    //   const midPoint = Cesium.Cartesian3.midpoint(p1, p2, new Cesium.Cartesian3());
    //   const direction = Cesium.Cartesian3.subtract(p2, p1, new Cesium.Cartesian3());
    //   Cesium.Cartesian3.normalize(direction, direction);
    //   const up = Cesium.Cartesian3.normalize(midPoint, new Cesium.Cartesian3());
    //   const right = Cesium.Cartesian3.cross(direction, up, new Cesium.Cartesian3());
    //   Cesium.Cartesian3.normalize(right, right);

    //   const offset = Cesium.Cartesian3.multiplyByScalar(right, -offsetMeters, new Cesium.Cartesian3());
    //   const p1Offset = Cesium.Cartesian3.add(p1, offset, new Cesium.Cartesian3());
    //   const p2Offset = Cesium.Cartesian3.add(p2, offset, new Cesium.Cartesian3());
    //   return [p1Offset, p2Offset];
    // }, false);

    polylineMap[reverseKey].polyline.positions = new Cesium.CallbackProperty(time => {
      const _p1 = toEnt.position.getValue(time);
      const _p2 = fromEnt.position.getValue(time);
      const p1 = toAbsolute(_p1, viewer);
      const p2 = toAbsolute(_p2, viewer);
      if (!p1 || !p2) return [];

      const mid = Cesium.Cartesian3.midpoint(p1, p2, new Cesium.Cartesian3());
      const dir = safeNormalize(Cesium.Cartesian3.subtract(p2, p1, new Cesium.Cartesian3()));
      if (!dir) return [p1, p2];

      const up = geodeticUp(mid);
      const right = safeNormalize(Cesium.Cartesian3.cross(dir, up, new Cesium.Cartesian3()));
      if (!right) return [p1, p2];

      const offset = Cesium.Cartesian3.multiplyByScalar(right, -offsetMeters, new Cesium.Cartesian3());
      return [
        Cesium.Cartesian3.add(p1, offset, new Cesium.Cartesian3()),
        Cesium.Cartesian3.add(p2, offset, new Cesium.Cartesian3())
      ];
    }, false);


    // ★ 反向线的 label 向下偏移
    const revLabel = polylineMap[`${reverseKey}_label`];
    if (revLabel?.label) {
      revLabel.label.pixelOffset = new Cesium.Cartesian2(0, labelOffsetPx);
    }
    // ★ 同时确保正向线的 label 被上移（如果它是先创建的可能还是 0）
    if (labelEnt?.label) {
      labelEnt.label.pixelOffset = new Cesium.Cartesian2(0, -labelOffsetPx);
    }
  }

  return lineEnt;
};


/**
 * 计算相对 ENU 网格点
 */
export const computeRelativeSurfacePoints = (nRow, nCol, R_max) => {
  const arr = Array(nRow)
  for (let i = 0; i < nRow; i++) {
    arr[i] = Array(nCol)
    const ele = (i / (nRow - 1)) * 180 - 90
    const cosE = Math.cos(Cesium.Math.toRadians(ele)), sinE = Math.sin(Cesium.Math.toRadians(ele))
    for (let j = 0; j < nCol; j++) {
      const az = Cesium.Math.toRadians((j / (nCol - 1)) * 360)
      const r = R_max[i][j]
      arr[i][j] = new Cesium.Cartesian3(r * cosE * Math.sin(az), r * cosE * Math.cos(az), r * sinE)
    }
  }
  return arr
}

// ================================
// 地图图层 & 视角设置
// ================================
/**
 * 清空并加载地图基础 + 路网图层
 */
// export const setupLayers = async viewer => {
//   // 1. —— 影像层（不变）—————————————————————————
//   viewer.imageryLayers.removeAll()


//   const streetProvider = new Cesium.UrlTemplateImageryProvider({
//     url: 'http://127.0.0.1:32768/overlay/{z}/{x}/{y}.png', // 离线瓦片
//     // url: 'https://webst01.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}',
//     tilingScheme: new Cesium.WebMercatorTilingScheme(),
//     maximumLevel: 18
//   })
//   const satelliteProvider = new Cesium.UrlTemplateImageryProvider({
//     url: 'http://127.0.0.1:32768/satellite/{z}/{x}/{y}.jpg',
//     // url: 'http://127.0.0.1:32767/{z}/{x}/{y}.jpg',
//     // url: 'http://127.0.0.1:32766/{z}/{x}/{y}.jpg',
//     // url: 'https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
//     tilingScheme: new Cesium.WebMercatorTilingScheme(),
//     maximumLevel: 18,
//     alpha: 0.85
//   })

//   const satelliteLayer = viewer.imageryLayers.addImageryProvider(satelliteProvider)
//   const streetLayer = viewer.imageryLayers.addImageryProvider(streetProvider)

//   streetLayer.show = true
//   satelliteLayer.show = true


//   // 2. —— 地形部分：提前准备两套 provider ——————————
//   const ellipsoidProvider = new Cesium.EllipsoidTerrainProvider() // “关闭地形”时用
//   let qmeshProvider     // 真实 DEM

//   try {
//     qmeshProvider = await Cesium.CesiumTerrainProvider.fromUrl(
//       'http://127.0.0.1:32768/terrian/',
//       { requestWaterMask: true, requestVertexNormals: true }
//     )
//   } catch (e) {
//     console.warn('加载离线 DEM 失败，退回椭球地形', e)
//     qmeshProvider = ellipsoidProvider
//   }

//   // 默认先用真实地形（你也可以先用 ellipsoidProvider）
//   viewer.terrainProvider = qmeshProvider


//   // 3. —— 内部状态标记：目前是否开启地形 ——————————
//   let terrainEnabled = viewer.terrainProvider !== ellipsoidProvider


//   // 4. —— 对外提供三个开关 ——————————————————————
//   function toggleStreet() { streetLayer.show = !streetLayer.show }
//   function toggleSatellite() { satelliteLayer.show = !satelliteLayer.show }


//   function toggleTerrain() {
//     terrainEnabled = !terrainEnabled
//     viewer.terrainProvider = terrainEnabled ? qmeshProvider : ellipsoidProvider
//   }

//   return {
//     toggleStreet,
//     toggleSatellite,
//     toggleTerrain
//   }
// }

export const setupLayers = async (viewer) => {
  viewer.imageryLayers.removeAll()

  // ★ 用自定义的 tilingScheme（把 GCJ02 影像按 WGS84 显示）
  const amapTiling = new AmapMercatorTilingScheme()

  const streetProvider = new Cesium.UrlTemplateImageryProvider({
    url: 'http://127.0.0.1:32768/overlay/{z}/{x}/{y}.png',
    tilingScheme: amapTiling,          // ★ 关键
    maximumLevel: 18
  })

  const satelliteProvider = new Cesium.UrlTemplateImageryProvider({
    url: 'http://127.0.0.1:32768/satellite/{z}/{x}/{y}.jpg',
    tilingScheme: amapTiling,          // ★ 关键
    maximumLevel: 18,
    alpha: 0.85
  })

  const satelliteLayer = viewer.imageryLayers.addImageryProvider(satelliteProvider)
  const streetLayer = viewer.imageryLayers.addImageryProvider(streetProvider)
  streetLayer.show = true
  satelliteLayer.show = true

  // 地形保持 WGS84（无需改）
  const ellipsoidProvider = new Cesium.EllipsoidTerrainProvider()
  let qmeshProvider
  try {
    qmeshProvider = await Cesium.CesiumTerrainProvider.fromUrl(
      'http://127.0.0.1:32768/terrian/',
      { requestWaterMask: true, requestVertexNormals: true }
    )
  } catch {
    qmeshProvider = ellipsoidProvider
  }
  viewer.terrainProvider = qmeshProvider

  let terrainEnabled = viewer.terrainProvider !== ellipsoidProvider
  function toggleStreet() { streetLayer.show = !streetLayer.show }
  function toggleSatellite() { satelliteLayer.show = !satelliteLayer.show }
  function toggleTerrain() { terrainEnabled = !terrainEnabled; viewer.terrainProvider = terrainEnabled ? qmeshProvider : ellipsoidProvider }

  return { toggleStreet, toggleSatellite, toggleTerrain }
}


/**
 * 设置相机默认视角
 */
export function setDefaultView(viewer, { lon, lat, alt, cartesian, heading = 0, pitch = -90, roll = 0 }) {
  const dest = cartesian
    ? new Cesium.Cartesian3(cartesian.x, cartesian.y, cartesian.z)
    : Cesium.Cartesian3.fromDegrees(lon, lat, alt)
  viewer.camera.flyTo({ destination: dest, orientation: { heading: Cesium.Math.toRadians(heading), pitch: Cesium.Math.toRadians(pitch), roll } })
}


// ================================
// 地图绘制
// ================================

// thickness: 想要往上“拉”多少米
export function drawPrism(viewer, coords, thickness = 10000) {
  if (!viewer || coords.length < 3) return

  // 1) 计算底面顶点（Cartesian3）和各顶点的最小／最大高程数组
  const bottomPositions = coords.map(p =>
    Cesium.Cartesian3.fromDegrees(+p.longitude, +p.latitude, +p.alt)
  )
  const minHeights = coords.map(p => +p.alt)
  const maxHeights = coords.map(p => +p.alt + thickness)

  // 2) 底面
  const bottomInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.PolygonGeometry({
      polygonHierarchy: { positions: bottomPositions },
      perPositionHeight: true
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.BLUE.withAlpha(0.3))
    }
  })

  // 3) 顶面——同样顶点，只是高度用 maxHeights
  const topPositions = coords.map(p =>
    Cesium.Cartesian3.fromDegrees(+p.longitude, +p.latitude, +p.alt + thickness)
  )
  const topInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.PolygonGeometry({
      polygonHierarchy: { positions: topPositions },
      perPositionHeight: true
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.BLUE.withAlpha(0.6))

    }
  })

  // 4) 侧面，用 WallGeometry 自动根据 min/max 画墙
  const wallInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.WallGeometry({
      positions: bottomPositions,
      minimumHeights: minHeights,
      maximumHeights: maxHeights,
      perPositionHeight: true
    }),
    attributes: { color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.YELLOW) }
  })

  // 5) 一起提交给 Primitive
  const primitive = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: [bottomInstance, topInstance, wallInstance],
      appearance: new Cesium.PerInstanceColorAppearance({ flat: true, translucent: false })
    })
  )

  return primitive
}

// 绘制圆柱体，顶面在指定高度，底面贴地
export function drawCircle(viewer, c = {}) {
  if (!viewer) return null
  const lon = Number(c.longitude) || 0
  const lat = Number(c.latitude) || 0
  const alt = Number(c.alt) || 10000
  const r = Number(c.radius)
  if (isNaN(lon) || isNaN(lat) || isNaN(r)) return null

  const radiusInMeters = r * 1000 // 转换为米
  const groundHeight = 0 // 地面高度
  const cylinderHeight = alt - groundHeight // 圆柱体高度（从地面到指定高度）

  // 生成圆周上的点（顶面圆形）
  const topPositions = []
  const bottomPositions = []
  const segments = 64 // 圆周分段数

  for (let i = 0; i <= segments; i++) {
    const angle = (i / segments) * 2 * Math.PI
    const offsetLon =
      (radiusInMeters * Math.cos(angle)) / (111320 * Math.cos((lat * Math.PI) / 180))
    const offsetLat = (radiusInMeters * Math.sin(angle)) / 111320

    // 顶面圆形点
    topPositions.push(Cesium.Cartesian3.fromDegrees(lon + offsetLon, lat + offsetLat, alt))
    // 底面圆形点（贴地）
    bottomPositions.push(
      Cesium.Cartesian3.fromDegrees(lon + offsetLon, lat + offsetLat, groundHeight)
    )
  }

  // 1. 顶面圆形
  const topInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.PolygonGeometry({
      polygonHierarchy: { positions: topPositions },
      perPositionHeight: true
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED.withAlpha(0.4))
    }
  })

  // 2. 底面圆形（贴地）
  const bottomInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.PolygonGeometry({
      polygonHierarchy: { positions: bottomPositions },
      perPositionHeight: true
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED.withAlpha(0.2))
    }
  })

  // 3. 侧面圆柱壁
  const wallInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.WallGeometry({
      positions: topPositions.slice(0, -1), // 去掉重复的最后一个点
      minimumHeights: new Array(segments).fill(groundHeight),
      maximumHeights: new Array(segments).fill(alt)
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.RED.withAlpha(0.1))
    }
  })

  // 合并所有几何实例创建圆柱体
  const cylinder = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: [topInstance, bottomInstance, wallInstance],
      appearance: new Cesium.PerInstanceColorAppearance({
        flat: true,
        translucent: true
      }),
      asynchronous: false
    })
  )

  viewer.scene.primitives.add(cylinder)
  return Cesium.Cartesian3.fromDegrees(lon, lat, alt)
}
