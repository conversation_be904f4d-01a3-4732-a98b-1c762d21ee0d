<script setup>
  import { taskPlanOptions } from '@/api/spectrumMng/taskMng.js'
  import {
    addDynamicLine,
    chooseScale,
    chooseModel,
    setupLayers,
    setDefaultView
  } from '@/utils/war.js'
  import EquipListCom from './components/EquipListCom.vue'
  import RecordCom from './components/RecordCom.vue'
  import LogCom from './components/LogCom.vue'
  import ConflictCom from './components/ConflictCom.vue'
  import FreqGanttCom from './components/FreqGanttCom.vue'
  import EntityParameterPanel from '@/components/EntityParameterPanel/index.vue'
  import RtspDialog from './components/RtspDialog.vue'
  import SnrStatistic from './components/SnrStatistic.vue'
  import mapTools from '@/components/mapTools/index.vue'
  import MapZoomControl from '@/components/MapZoomControl/index.vue'
  import * as Cesium from 'cesium'
  import {
    Cartesian3,
    Color,
    HeadingPitchRoll,
    LabelStyle,
    ScreenSpaceEventHandler,
    ScreenSpaceEventType
  } from 'cesium'
  import { settings } from '@/utils/settings.js'
  import { ElMessage } from 'element-plus'
  import { debounce } from 'lodash'

  // 导入优化的hooks
  import { useMeasure } from '@/hooks/useMeasure.js'
  import { useSelectionIndicator } from '@/hooks/useSelectionIndicator.js'
  import useEntityParameterPanel from '@/hooks/useEntityParameterPanel.js'
  import useEntityManager from '@/hooks/useEntityManager.js'

  // 响应式状态
  const taskOptions = ref([]) // 任务选项
  const mapContainer = ref(null) // 地图容器
  const layers = ref(null) // 图层集合
  const warTaskId = ref(null) // 任务ID
  const isInitialized = ref(false) // 是否初始化
  const isRunning = ref(false) // 是否正在作战
  const isTakeOff = ref(false) // 是否起飞
  const showEquipListFlag = ref(false) // 是否显示设备列表
  const showRecordFlag = ref(false) // 是否显示作战记录
  const showLogFlag = ref(false) // 是否显示日志记录
  const showConflictFlag = ref(false) // 是否显示冲突列表
  const showGanttFlag = ref(false) // 是否显示频率甘特图
  const showRtspFlag = ref(false) // 是否显示视频流
  const showSnrStatisticsFlag = ref(false) // 是否显示信噪比统计

  const simulation = ref(false) // 作战状态
  const loading = ref(false) // 加载状态

  const firstSetView = ref(false) // 是否首次设置视角
  const toolboxExpanded = ref(false) // 工具箱展开状态
  const simulationTime = ref(0) // 作战时间（s）
  const simulationProgress = ref(0) // 作战进度
  const platformId = ref(null) // 平台ID
  const mapToolsConfig = ref([]) // 地图工具配置
  const equipmentList = ref([]) // 设备列表
  const recordTitle = ref('') // 作战记录标题
  const recordList = ref([]) // 作战记录
  const logList = ref([]) // 日志记录
  const conflictList = ref([]) // 冲突列表
  const rtspList = ref([]) // RTSP视频流列表
  const collaborationRanges = reactive({
    available: [],
    conflict: [],
    reserved: []
  })
  const categories = ref([]) // 频率甘特图legend（）

  // 非响应式状态
  let viewer = null
  // 添加双击状态标记
  let isDoubleClick = false
  let clickTimeout = null
  let tracks = new Map() // 轨迹集合（id唯一标识）
  let polylineMap = {} // 连线集合
  let socket = null // websocket连接
  let handler = null // 事件监听器
  let isMeasuring, isSpaceMeasuring, toggleMeasure, toggleSpaceMeasure, clearMeasure

  // 优化hooks
  const selectionIndicator = ref(null)
  const entityParameterPanel = ref(null)
  const entityManager = ref(null)

  // 添加工具激活状态
  const activeTools = ref(new Set()) // 使用Set存储激活的工具ID

  // ===================== 新增工具函数：稳定地形高 & 入历史点 =====================
  const terrainCache = new Map()
  function _quantKey(carto) {
    // 量化到 1e-6 度（~0.11m @赤道），减少缓存粒度
    return `${Cesium.Math.toDegrees(carto.longitude).toFixed(6)},${Cesium.Math.toDegrees(
      carto.latitude
    ).toFixed(6)}`
  }
  async function getStableTerrainHeight(viewer, cartoNoH) {
    const key = _quantKey(cartoNoH)
    if (terrainCache.has(key)) return terrainCache.get(key)

    const q = new Cesium.Cartographic(cartoNoH.longitude, cartoNoH.latitude, 0)
    const res = await Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [q]).catch(
      () => null
    )
    const h = res && res[0] && Number.isFinite(res[0].height) ? res[0].height : 0
    terrainCache.set(key, h)
    return h
  }

  /**
   * 把“相对地面位置”(cartesian.height=AGL) 转为“绝对高(terrain+AGL)”并入历史
   * 地面实体直接贴地(0)
   */
  async function pushHistoryPointFromRelative(track, cartesianRel, viewer) {
    const c = Cesium.Cartographic.fromCartesian(cartesianRel)

    if (track.isGroundEntity) {
      track.positionsHistory.push(Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, 0))
      return
    }

    const agl = c.height || 0
    const terrain = await getStableTerrainHeight(viewer, c)
    track.positionsHistory.push(
      Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, terrain + agl)
    )
  }

  /** 加载作战任务 */
  const loadTask = async () => {
    isInitialized.value = false
    loading.value = true
    initWebSocket()
  }

  /** 初始化WebSocket连接 */
  function initWebSocket() {
    // 清理旧连接
    if (socket) {
      socket.close()
      socket = null
    }
    // 创建新连接
    const socketUrl = settings.VITE_GLOB_WS + '/ws/war'
    socket = new WebSocket(socketUrl)

    // 设置WebSocket事件处理
    socket.onopen = handleWebSocketOpen
    socket.onmessage = handleWebSocketMessage
    socket.onclose = handleWebSocketClose
    socket.onerror = handleWebSocketError
  }

  /** WebSocket连接成功事件处理 */
  function handleWebSocketOpen() {
    // 发送加载作战任务指令
    socket.send(
      JSON.stringify({
        messageCode: '1',
        warTaskId: warTaskId.value
      })
    )
  }

  /** WebSocket消息接收事件处理 */
  function handleWebSocketMessage(event) {
    try {
      const res = JSON.parse(event.data)
      if (res.code !== 200) return
      isInitialized.value = true
      switch (res.messageCode) {
        case '1': // 加载作战任务
          ElMessage.success('作战任务加载成功！')
          loading.value = false
          break
        case '2': // 起飞
          ElMessage.success('起飞成功！')
          loading.value = false
          isTakeOff.value = false
          break
        case '20': // 模拟更新
          handleWarUpdate(res)
          break
        case '21': // 设备和记录更新
          handleEquipmentAndRecordUpdate(res)
          break
        case '22': // 频率甘特图更新
          handleGanttUpdate(res)
          break
        case '40': // 冲突更新
          handleConflictUpdate(res)
          break
        case '50': // 日志更新
          handleLogUpdate(res)
          break
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error)
    }
  }

  /** WebSocket连接关闭事件处理 */
  function handleWebSocketClose(event) {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    isRunning.value = false
  }

  /** WebSocket错误事件处理 */
  function handleWebSocketError(error) {
    console.error('WebSocket错误:', error)
  }

  /** 处理模拟更新数据 */
  function handleWarUpdate(res) {
    const resData = JSON.parse(res?.messageInfo)
    simulationProgress.value = resData.simulationProgress
    simulationTime.value = resData.simulationTime
    simulation.value = resData.simulation

    // 首次设置视图中心点
    if (!firstSetView.value) {
      const radarPosition = resData.warIngPlatformsMap.filter(item => item.platformModel === '3')
      const centerPoint = {
        lon: radarPosition[0].longitude,
        lat: radarPosition[0].latitude,
        alt: radarPosition[0].altitude + 1000
      }

      setDefaultView(viewer, centerPoint)
      firstSetView.value = true
    }

    const platforms = resData.warIngPlatformsMap
    const platformModel3 = platforms.filter(platform => platform.platformModel === '3')
    const otherPlatforms = platforms.filter(platform => platform.platformModel !== '3')
    const warIngPlatformsMap = [...platformModel3, ...otherPlatforms]

    // 更新平台位置
    // updatePlatformPositions(resData.warIngPlatformsMap || [])
    updatePlatformPositions(warIngPlatformsMap || [])

    // 更新通信连接线
    updateCommunicationLinks(resData.commLinks || [])
  }

  /** 更新通信连接线 */
  function updateCommunicationLinks(commLinks) {
    if (commLinks.length === 0) return
    commLinks.forEach(link => {
      const { sourcePlatformId, targetPlatformId, snr, jam, offLine } = link
      addDynamicLine({
        tracks,
        polylineMap,
        sourcePlatformId,
        targetPlatformId,
        signalStrength: snr,
        viewer,
        jam,
        offLine
      })
    })
  }

  /** 处理设备和记录更新 */
  function handleEquipmentAndRecordUpdate(res) {
    const resData = JSON.parse(res?.messageInfo)
    equipmentList.value = resData.equipmentRecordList
    recordList.value = resData.slActionRecordList
  }

  /** 处理日志更新 */
  function handleLogUpdate(res) {
    const resData = JSON.parse(res?.messageInfo)
    logList.value.push(...resData)
  }

  /** 处理冲突更新 */
  function handleConflictUpdate(res) {
    const resData = JSON.parse(res?.messageInfo)
    conflictList.value = resData
  }

  /** 处理甘特图更新 */
  function handleGanttUpdate(res) {
    // ① 解析消息
    const raw = JSON.parse(res?.messageInfo ?? '[]')
    if (!Array.isArray(raw) || raw.length === 0) return

    /* -----------------------------------------------------------
     * ② 先拿到类别名，初始化结果骨架
     * --------------------------------------------------------- */
    const categoryNames = raw.map(item => item.name) // 改名避免冲突
    const n = categoryNames.length
    const result = {
      available: Array.from({ length: n }, () => []),
      conflict: Array.from({ length: n }, () => []),
      reserved: Array.from({ length: n }, () => [])
    }

    /* -----------------------------------------------------------
     * ③ 拆扫描线事件：普通区段→(start,+1)/(end,-1)
     *    "安全备用频段"→直接进 reserved
     * --------------------------------------------------------- */
    const events = [] // { pos, delta:+1/-1, cat }
    raw.forEach((item, idx) => {
      const isReserved = /备用/.test(item.name)
      item.freqUseDeatils?.forEach(d => {
        const seg = [d.startFreq, d.endFreq] // Hz
        if (isReserved) {
          result.reserved[idx].push(seg)
        } else {
          events.push({ pos: d.startFreq, delta: +1, cat: idx })
          events.push({ pos: d.endFreq, delta: -1, cat: idx })
        }
      })
    })

    if (events.length) {
      /* ---------------------------------------------------------
       * ④ 扫描线：统计重叠度，填 available / conflict
       * ------------------------------------------------------- */
      events.sort((a, b) => (a.pos === b.pos ? a.delta - b.delta : a.pos - b.pos))

      const curCats = new Set() // 当前频点被哪些链路覆盖
      let prevPos = null
      for (const { pos, delta, cat } of events) {
        if (prevPos !== null && pos > prevPos && curCats.size) {
          const seg = [prevPos, pos] // 频段 [prev,pos)
          if (curCats.size === 1) {
            // 独占
            result.available[[...curCats][0]].push(seg)
          } else {
            // 冲突
            curCats.forEach(c => result.conflict[c].push(seg))
          }
        }
        // 更新覆盖集合
        delta === +1 ? curCats.add(cat) : curCats.delete(cat)
        prevPos = pos
      }
    }

    /* -----------------------------------------------------------
     * ⑤ 写回响应式对象（保持引用稳定，适合 Vue3 视图更新）
     * --------------------------------------------------------- */

    // 正确赋值给响应式变量
    categories.value = categoryNames
    // available/conflict/reserved
    ;['available', 'conflict', 'reserved'].forEach(key => {
      collaborationRanges[key].splice(0, collaborationRanges[key].length, ...result[key])
    })
  }

  /** 更新平台位置和轨迹图形 */
  function updatePlatformPositions(platforms) {
    if (platforms.length === 0) return
    const currentIds = new Set()
    let rtspKJG = '' // 可见光
    let rtspRCX = '' // 红外
    let skydoridcStatus = [] // 无人机云台

    platforms.forEach(item => {
      const { id, name, longitude, latitude, altitude, platformModel, equipmentFreqUseDeatils } =
        item
      currentIds.add(id)
      if (platformModel === '1') {
        if (rtspList.value.filter(item => item.id === id).length === 0) {
          rtspList.value.push(item)
        }
        rtspKJG = item.rtspKJG
        rtspRCX = item.rtspRCX
        skydoridcStatus = [
          {
            label: '航向角(°)',
            value: item.uavAllData?.skydoridcStatus?.yaw || '0'
          },
          {
            label: '俯仰角(°)',
            value: item.uavAllData?.skydoridcStatus?.pitch || '0'
          },
          {
            label: '横滚角(°)',
            value: item.uavAllData?.skydoridcStatus?.roll || '0'
          }
        ]
      }
      // 当前实时位置
      const position = Cartesian3.fromDegrees(
        parseFloat(longitude),
        parseFloat(latitude),
        parseFloat(altitude)
      )

      const hpr = new HeadingPitchRoll(Cesium.Math.toRadians(0), 0, 0)
      const orientation = Cesium.Transforms.headingPitchRollQuaternion(position, hpr)

      // 如果这个 id 第一次出现，就创建 marker + historyLine
      if (!tracks.has(id)) {
        createNewTrack(
          id,
          name,
          position,
          orientation,
          platformModel,
          rtspKJG,
          rtspRCX,
          skydoridcStatus,
          equipmentFreqUseDeatils
        )
      } else {
        updateExistingTrack(id, position, skydoridcStatus)
      }
    })

    // 清理不再返回的 id
    cleanupRemovedTracks(currentIds)
  }

  function toHistoryPosition(cartesian, isGround, viewer) {
    const c = Cesium.Cartographic.fromCartesian(cartesian)
    const terrain = viewer.scene.globe.getHeight(c) || 0

    if (isGround) {
      // 地面轨迹画在地形面上（或你想稍微抬一点就 terrain + 0.5）
      return Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, terrain)
    } else {
      // 空中轨迹 = 地形高度 + 相对高度（cartesian.height 里存的就是相对高）
      const relative = c.height || 0
      return Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, terrain + relative)
    }
  }

  /** 创建新的轨迹 */
  // ===================== createNewTrack（替换） =====================
  function createNewTrack(
    id,
    name,
    position, // 这里 position 是“绝对高” (来自服务端 altitude)
    orientation,
    platformModel,
    rtspKJG,
    rtspRCX,
    skydoridcStatus,
    equipmentFreqUseDeatils
  ) {
    const isGroundEntity = platformModel !== '1'

    // —— 统一：模型/标签用“相对地面” —— //
    let finalPosition = position

    if (isGroundEntity) {
      // 地面：把 height 归 0（贴地）
      const c = Cesium.Cartographic.fromCartesian(position)
      finalPosition = Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, 0)
    } else {
      // 空中：绝对高 -> 相对高
      const c = Cesium.Cartographic.fromCartesian(position)
      const absolute = c.height || 0
      // 此处为了实时性，先用 getHeight 近似；历史线会用稳定地形高
      const terrainQuick = viewer.scene.globe.getHeight(c) || 0
      const rel = Math.max(absolute - terrainQuick, 0)
      finalPosition = Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, rel)
    }

    const positionData = {
      current: finalPosition,
      target: finalPosition,
      startTime: Date.now(),
      duration: 1000
    }

    const marker = viewer.entities.add({
      id,
      name,
      rtspKJG,
      rtspRCX,
      skydoridcStatus,
      position: new Cesium.CallbackProperty(() => {
        const now = Date.now()
        const t = Math.min((now - positionData.startTime) / positionData.duration, 1)
        if (t >= 1) return positionData.target
        return Cesium.Cartesian3.lerp(
          positionData.current,
          positionData.target,
          t,
          new Cesium.Cartesian3()
        )
      }, false),
      properties: {},
      equipmentFreqUseDeatils: equipmentFreqUseDeatils || [],
      orientation,
      model: {
        uri: chooseModel(platformModel),
        scale: chooseScale(platformModel),
        minimumPixelSize: 30,
        maximumScale: 400,
        heightReference: isGroundEntity
          ? Cesium.HeightReference.CLAMP_TO_GROUND
          : Cesium.HeightReference.RELATIVE_TO_GROUND,
        silhouetteColor: Color.YELLOW,
        silhouetteSize: 2
      },
      label: {
        text: name,
        font: '12px sans-serif',
        style: LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 2,
        pixelOffset: new Cesium.Cartesian2(0, -20),
        heightReference: isGroundEntity
          ? Cesium.HeightReference.CLAMP_TO_GROUND
          : Cesium.HeightReference.RELATIVE_TO_GROUND
      }
    })

    const positionsHistory = []
    const historyLine = viewer.entities.add({
      id: `${id}_historyLine`,
      polyline: {
        // 历史线只读数组；数组内容由 pushHistoryPointFromRelative 动态追加
        positions: new Cesium.CallbackProperty(() => positionsHistory, false),
        width: 2,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.CYAN.withAlpha(0.8),
          dashLength: 6.0,
          dashPattern: 255.0
        }),
        clampToGround: isGroundEntity // 空中轨迹不贴地
        // 不再设置 polyline.heightReference（部分版本不支持/不稳定）
      }
    })

    const track = { marker, historyLine, positionsHistory, positionData, isGroundEntity }
    tracks.set(id, track)

    // 首帧：用“稳定地形高 + AGL”入历史，规避第一帧抖动
    pushHistoryPointFromRelative(track, finalPosition, viewer).catch(console.warn)
  }

  /** 更新现有轨迹 */
  // ===================== updateExistingTrack（替换） =====================
  async function updateExistingTrack(id, position, skydoridcStatus) {
    const track = tracks.get(id)
    if (!track) return

    let finalPosition = position
    const c = Cesium.Cartographic.fromCartesian(position)

    if (track.isGroundEntity) {
      finalPosition = Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, 0)
    } else {
      const absolute = c.height || 0
      // 目标用于“模型插值”：可先用快速地形，视觉连续；历史线另用稳定地形
      const terrainQuick = viewer.scene.globe.getHeight(c) || 0
      const rel = Math.max(absolute - terrainQuick, 0)
      finalPosition = Cesium.Cartesian3.fromRadians(c.longitude, c.latitude, rel)
    }

    pushHistoryPointFromRelative(track, track.positionData.target, viewer)

    // 驱动模型相对地面插值
    track.positionData.current = track.positionData.target
    track.positionData.target = finalPosition
    track.positionData.startTime = Date.now()

    // 插值完成后，把终点按“稳定地形高 + AGL”落入历史
    // const duration = track.positionData.duration
    // setTimeout(async () => {
    //   // 防止中途被重置导致重复入点
    //   if (Date.now() - track.positionData.startTime >= duration - 2) {
    //     await pushHistoryPointFromRelative(track, track.positionData.target, viewer)
    //   }
    // }, duration)

    // 更新参数面板/选中实体信息
    updateSelectedEntityIfMatches(id, finalPosition, skydoridcStatus, track)
  }

  /** 如果匹配选中的实体，更新其位置和范围 */
  function updateSelectedEntityIfMatches(id, position, skydoridcStatus, track) {
    // 使用entityManager更新选中实体位置
    if (
      entityManager.value?.selectedEntity.entity &&
      track.marker.id === entityManager.value.selectedEntity.entity.id
    ) {
      entityManager.value.updateSelectedEntityPosition(position)

      // 更新参数面板中的坐标信息
      const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(position)
      const coordinates = {
        lon: Cesium.Math.toDegrees(cartographic.longitude),
        lat: Cesium.Math.toDegrees(cartographic.latitude),
        alt: cartographic.height
      }
      entityParameterPanel.value?.updateEntityCoordinates(coordinates)
      entityParameterPanel.value?.updateEntitySkyDORIDCStatus(skydoridcStatus)
    }
  }

  /** 清理已移除的轨迹 */
  function cleanupRemovedTracks(currentIds) {
    for (const [oldId, track] of tracks) {
      if (!currentIds.has(oldId) && !track.isSubStation) {
        // 清理CallbackProperty引用
        if (track.marker?.position?._callback) {
          track.marker.position._callback = null
        }

        viewer.entities.remove(track.marker)
        if (track.historyLine) viewer.entities.remove(track.historyLine)

        // 清理轨迹数据
        if (track.positionsHistory) {
          track.positionsHistory.length = 0
        }

        tracks.delete(oldId)
      }
    }
  }

  function takeOff() {
    if (socket && socket.readyState === WebSocket.OPEN) {
      // 发送起飞指令
      loading.value = true
      isTakeOff.value = true
      socket.send(JSON.stringify({ messageCode: '2', warTaskId: warTaskId.value }))
    }
  }

  /** 启动作战 */
  function startRun() {
    if (socket && socket.readyState === WebSocket.OPEN) {
      // 发送开始作战指令
      socket.send(JSON.stringify({ messageCode: '10' }))
      isRunning.value = true
    } else {
      console.log('WebSocket未连接，尝试重新连接')
      initWebSocket()
    }
  }

  /** 停止作战 */
  function stopRun() {
    if (socket) {
      // 发送结束作战指令
      socket.send(JSON.stringify({ messageCode: '11' }))
      ElMessage.success('作战停止')
      isRunning.value = false
      simulation.value = false
      platformId.value = null
    }
  }

  /** 关闭WebSocket连接 */
  function closeWebSocket() {
    if (socket) {
      // 移除所有事件监听器
      socket.onopen = null
      socket.onmessage = null
      socket.onclose = null
      socket.onerror = null
      socket.close()
      socket = null
    }
  }

  /**  初始化 Cesium 地图 */
  function initMap() {
    viewer = new Cesium.Viewer(mapContainer.value, {
      baseLayerPicker: false,
      infoBox: false,
      geocoder: false,
      homeButton: false,
      sceneModePicker: false,
      navigationHelpButton: false,
      animation: false,
      timeline: false,
      fullscreenButton: true,
      selectionIndicator: false, // 禁用默认选择指示器
      fullscreenElement: mapContainer.value
    })

    // 隐藏版权
    viewer._cesiumWidget._creditContainer.style.display = 'none'

    // 禁止左键双击切换视角
    viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)

    // 初始化hooks
    selectionIndicator.value = useSelectionIndicator(viewer)
    entityParameterPanel.value = useEntityParameterPanel()
    entityManager.value = useEntityManager(viewer)

    // 单击实体回调 - 显示选择指示器（添加延时避免与双击冲突）
    handler = new ScreenSpaceEventHandler(viewer.scene.canvas)
    handler.setInputAction(click => {
      const picked = viewer.scene.pick(click.position)
      if (Cesium.defined(picked) && picked.id) {
        // 清除之前的延时
        if (clickTimeout) {
          clearTimeout(clickTimeout)
          clickTimeout = null
        }

        // 延时执行单击逻辑，如果期间发生双击则取消
        clickTimeout = setTimeout(() => {
          if (!isDoubleClick) {
            onEntitySingleClick(picked.id, click.position)
          }
          isDoubleClick = false
          clickTimeout = null
        }, 150) // 减少延时到150ms
      } else {
        // 点击空白处，立即执行
        selectionIndicator.value?.hideSelectionIndicator()
        entityParameterPanel.value?.hideParameterPanel()
      }
    }, ScreenSpaceEventType.LEFT_CLICK)

    // 双击实体回调 - 立即显示参数面板
    handler.setInputAction(click => {
      const picked = viewer.scene.pick(click.position)
      if (Cesium.defined(picked) && picked.id) {
        // 标记为双击，取消单击延时
        isDoubleClick = true
        if (clickTimeout) {
          clearTimeout(clickTimeout)
          clickTimeout = null
        }
        // 立即执行双击逻辑
        onEntityDoubleClick(picked.id, click.position)
      }
    }, ScreenSpaceEventType.LEFT_DOUBLE_CLICK)

    setupLayers(viewer).then(res => {
      layers.value = res
    })

    setDefaultView(viewer, { lon: 118.493337, lat: 34.457876, alt: 10000 })

    // **在这里** 调用 useMeasure，把 viewer 传进去
    const measure = useMeasure(viewer, mapContainer)
    isMeasuring = measure.isMeasuring
    isSpaceMeasuring = measure.isSpaceMeasuring
    toggleMeasure = measure.toggleMeasure
    toggleSpaceMeasure = measure.toggleSpaceMeasure
    clearMeasure = measure.clearMeasure
  }

  /** 当实体被单击时触发 - 执行所有逻辑除了显示参数面板 */
  function onEntitySingleClick(entity, clickPosition) {
    // 如果是实体类型，执行所有逻辑
    if (entity.id && selectionIndicator.value?.isEntityType(entity)) {
      recordTitle.value = entity.name + '行动记录'

      // 使用entityManager设置选中实体
      entityManager.value?.setSelectedEntity(entity)
      platformId.value = entity.id

      // 显示选择指示器
      selectionIndicator.value?.showSelectionIndicator(entity)

      // 更新实体坐标信息
      const position = selectionIndicator.value?.getEntityPosition(entity)
      if (position) {
        const cartographic = viewer.scene.globe.ellipsoid.cartesianToCartographic(position)
        const coordinates = {
          lon: Cesium.Math.toDegrees(cartographic.longitude),
          lat: Cesium.Math.toDegrees(cartographic.latitude),
          alt: cartographic.height
        }
        entityParameterPanel.value?.updateEntityCoordinates(coordinates)
      }

      // 发送WebSocket消息获取设备信息
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            messageCode: '21',
            platformId: platformId.value
          })
        )
      }
    }
  }

  /** 当实体被双击时触发 - 优化响应速度 */
  function onEntityDoubleClick(entity, clickPosition) {
    // 如果是实体类型，立即显示参数面板
    if (entity.id && selectionIndicator.value?.isEntityType(entity)) {
      // 立即计算面板位置
      const panelPosition = {
        x: clickPosition.x,
        y: clickPosition.y
      }

      // 立即显示参数面板，不等待任何延时
      entityParameterPanel.value?.showParameterPanel(entity, panelPosition)
    }
  }

  /** 任务切换事件，重置推演状态和视图等*/
  const taskChangeEvent = () => {
    // 停止 WebSocket
    closeWebSocket()

    // 重置状态
    isInitialized.value = false
    firstSetView.value = false
    showRecordFlag.value = false
    showEquipListFlag.value = false
    simulationProgress.value = 0
    simulationTime.value = 0
    equipmentList.value = []
    recordList.value = []
    // 清理数据结构
    tracks.clear()

    // 重置entityManager
    entityManager.value?.initSelectedEntity()

    // 安全清理 polylineMap
    Object.keys(polylineMap).forEach(key => {
      try {
        const entity = polylineMap[key]
        if (entity && viewer.entities.contains(entity)) {
          // 清理CallbackProperty
          if (entity.polyline?.positions?._callback) {
            entity.polyline.positions._callback = null
          }
          viewer.entities.remove(entity)
        }
      } catch (error) {
        console.warn(`清理连线失败: ${error.message}`)
      }
      delete polylineMap[key]
    })
    // 强制垃圾回收提示
    if (window.gc) {
      window.gc()
    }
    // 清理剩余实体
    viewer.entities.removeAll()
    // 清理内存资源
    cleanupMemoryResources()
  }

  /** 显示/隐藏行动记录面板 */
  const showRecord = debounce(() => {
    if (!platformId.value) {
      ElMessage.warning('请先选择作战平台')
      return
    }
    showRecordFlag.value = !showRecordFlag.value
  }, 300)

  /** 显示/隐藏设备列表面板*/
  const showEquipList = debounce(() => {
    if (!platformId.value) {
      ElMessage.warning('请先选择作战平台')
      return
    }
    showEquipListFlag.value = !showEquipListFlag.value
  }, 300)

  /** 显示/隐藏冲突分析列表面板*/
  const showConflict = debounce(() => {
    showConflictFlag.value = !showConflictFlag.value
  }, 300)

  /** 显示/隐藏频率甘特图 */
  const showGantt = debounce(() => {
    showGanttFlag.value = !showGanttFlag.value
  }, 300)

  /** 显示/隐藏视频流 */
  const showRtsp = debounce(() => {
    showRtspFlag.value = !showRtspFlag.value
  }, 300)

  /** 显示/隐藏作战日志 */
  const showWarLog = debounce(() => {
    showLogFlag.value = !showLogFlag.value
  }, 300)

  /** 显示/隐藏信噪比统计 */
  const showSnrStatistics = debounce(() => {
    if (!warTaskId.value) {
      ElMessage.warning('请先选择作战任务')
      return
    }
    showSnrStatisticsFlag.value = !showSnrStatisticsFlag.value
  }, 300)

  /** 清理内存资源 */
  function cleanupMemoryResources() {
    // 清理entityManager
    entityManager.value?.cleanupRangeEntities()
  }

  // 定义态势工具配置
  const situationToolsConfig = {
    radar: {
      id: 'radarPrimitive',
      tooltip: '雷达覆盖范围',
      icon: 'leida',
      source: 'iconfont',
      action: () => toggleSituationTool('radarPrimitive')
    },
    communication: {
      id: 'commRangeEntity',
      tooltip: '通信覆盖范围',
      icon: 'tongxin',
      source: 'iconfont',
      action: () => toggleSituationTool('commRangeEntity')
    }
  }

  // 切换态势工具状态
  const toggleSituationTool = toolId => {
    if (!entityManager.value) return

    // 调用entityManager的切换方法，获取新状态
    const newState = entityManager.value.handleRangeToggle(toolId)

    // 更新激活状态
    if (newState) {
      activeTools.value.add(toolId)
    } else {
      activeTools.value.delete(toolId)
    }
  }

  // 检查工具是否激活
  const isToolActive = toolId => {
    return activeTools.value.has(toolId)
  }

  // 更新态势工具配置
  const updateSituationTools = requiredTools => {
    // 获取当前态势工具的ID列表
    const currentSituationToolIds = Object.values(situationToolsConfig).map(tool => tool.id)

    // 移除不需要的态势工具，并清理其激活状态
    mapToolsConfig.value = mapToolsConfig.value.filter(tool => {
      const isSituationTool = currentSituationToolIds.includes(tool.id)
      if (isSituationTool) {
        const toolType = Object.keys(situationToolsConfig).find(
          key => situationToolsConfig[key].id === tool.id
        )
        const shouldKeep = requiredTools.includes(toolType)

        // 如果不保留这个工具，清理其激活状态
        if (!shouldKeep) {
          activeTools.value.delete(tool.id)
          // 清理对应的范围实体
          if (tool.id === 'commRangeEntity') {
            entityManager.value?.cleanupCommRangeEntity?.()
          } else if (tool.id === 'radarPrimitive') {
            entityManager.value?.cleanupRadarEntity?.()
          }
        }

        return shouldKeep
      }
      return true // 保留非态势工具
    })

    // 添加需要的态势工具（避免重复添加）
    requiredTools.forEach(toolType => {
      const toolConfig = situationToolsConfig[toolType]
      const exists = mapToolsConfig.value.some(tool => tool.id === toolConfig.id)

      if (!exists) {
        // 在基础工具后面添加态势工具，包含激活状态
        mapToolsConfig.value.splice(-4, 0, {
          ...toolConfig,
          active: computed(() => isToolActive(toolConfig.id))
        })
      }
    })
  }

  // 移除所有态势工具
  const removeSituationTools = () => {
    const situationToolIds = Object.values(situationToolsConfig).map(tool => tool.id)

    // 清理激活状态
    situationToolIds.forEach(toolId => {
      activeTools.value.delete(toolId)
    })

    // 移除工具配置
    mapToolsConfig.value = mapToolsConfig.value.filter(tool => !situationToolIds.includes(tool.id))

    // 清理范围实体
    entityManager.value?.cleanupRangeEntities()
  }

  onMounted(async () => {
    initMap()
    await taskPlanOptions().then(res => {
      if (res.code === 200) {
        taskOptions.value = res.data
      }
    })
    mapToolsConfig.value = [
      {
        id: 'measureH',
        tooltip: '水平测距',
        icon: 'chizi',
        source: 'iconfont',
        action: toggleMeasure
      },
      {
        id: 'measureV',
        tooltip: '空间测距',
        icon: 'chizi',
        source: 'iconfont',
        action: toggleSpaceMeasure
      },
      {
        id: 'clear',
        tooltip: '清除测距',
        icon: 'qingchu',
        source: 'iconfont',
        action: clearMeasure
      },
      {
        id: 'recordList',
        tooltip: '行动记录',
        icon: 'list',
        source: 'iconfont',
        action: showRecord,
        active: computed(() => showRecordFlag.value)
      },
      {
        id: 'equipList',
        tooltip: '设备列表',
        icon: 'shebeiliebiao',
        source: 'iconfont',
        action: showEquipList,
        active: computed(() => showEquipListFlag.value)
      },
      {
        id: 'conflictList',
        tooltip: '冲突分析',
        icon: 'chongtufenxi',
        source: 'iconfont',
        action: showConflict,
        active: computed(() => showConflictFlag.value)
      },
      {
        id: 'gantt',
        tooltip: '频率甘特图',
        icon: 'pinpufenxi',
        source: 'iconfont',
        action: showGantt,
        active: computed(() => showGanttFlag.value)
      },
      {
        id: 'rtsp',
        tooltip: '视频流',
        icon: 'video',
        source: 'iconfont',
        action: showRtsp,
        active: computed(() => showRtspFlag.value)
      },
      {
        id: 'warLog',
        tooltip: '作战日志',
        icon: 'rizhi',
        source: 'iconfont',
        action: showWarLog,
        active: computed(() => showLogFlag.value)
      },
      {
        id: 'snrStatistics',
        tooltip: 'SNR统计',
        icon: 'SNR',
        source: 'iconfont',
        action: showSnrStatistics,
        active: computed(() => showSnrStatisticsFlag.value)
      }
    ]
  })

  onBeforeUnmount(() => {
    // 清理内存资源
    cleanupMemoryResources()
    closeWebSocket()
    viewer && viewer.destroy()
    handler?.destroy && handler.destroy()
  })

  // 监听选中实体变化，动态管理态势工具
  watch(
    () => entityManager.value?.selectedEntity?.entity,
    (newEntity, oldEntity) => {
      if (!newEntity) {
        // 没有选中实体时，移除所有态势工具
        removeSituationTools()
        return
      }

      const entityName = newEntity.name || ''
      const requiredTools = []

      // 根据实体名称确定需要的工具
      if (entityName.includes('雷达')) {
        requiredTools.push('radar')
      }
      if (entityName.includes('无人机') || entityName.includes('通信')) {
        requiredTools.push('communication')
      }

      // 更新态势工具配置
      updateSituationTools(requiredTools)
    },
    {
      immediate: true,
      deep: true
    }
  )
</script>

<template>
  <div v-loading="loading" class="w-full h-full">
    <!-- 功能面板 -->
    <div class="flex items-center gap-x-4">
      <el-select
        style="width: 200px"
        v-model="warTaskId"
        placeholder="请选择"
        @change="taskChangeEvent"
      >
        <el-option
          v-for="item in taskOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div class="flex items-center justify-between">
        <div>
          <span>作战状态：</span>
          <span :class="{ 'text-red-500': !simulation, 'text-green-400': simulation }">
            {{ simulation ? '运行中' : '已停止' }}
          </span>
        </div>
        <div class="mx-4"> 作战时间：{{ simulationTime }} </div>
      </div>
      <el-button type="primary" @click="loadTask" :disabled="!warTaskId || isInitialized">
        加载任务
      </el-button>
      <el-button
        type="primary"
        @click="takeOff"
        :disabled="!isInitialized || isRunning || isTakeOff"
      >
        起飞
      </el-button>
      <el-button
        type="primary"
        @click="startRun"
        :disabled="!isInitialized || isRunning || isTakeOff"
      >
        启动作战
      </el-button>
      <el-button
        type="primary"
        @click="stopRun"
        :disabled="(!isInitialized && !isRunning) || isTakeOff"
      >
        停止作战
      </el-button>
    </div>
    <!-- 进度条 -->
    <el-progress
      class="my-3"
      :text-inside="true"
      :stroke-width="18"
      :percentage="simulationProgress"
    >
      <span>{{ simulationProgress }}%</span>
    </el-progress>
    <div ref="mapContainer" class="mapContainer w-full relative cursor-pointer">
      <!-- 工具栏 -->
      <map-tools v-model:expanded="toolboxExpanded" :tools="mapToolsConfig"> </map-tools>

      <!-- 整合的缩放和图层控件 -->
      <div class="absolute bottom-8 right-0 z-20">
        <MapZoomControl :viewer="viewer" :layers="layers" />
      </div>

      <!-- 实体参数面板 -->
      <EntityParameterPanel
        :show-panel="entityParameterPanel?.showPanel || false"
        :show-video="entityParameterPanel?.selectedEntityInfo?.type === '无人机'"
        :selected-entity-info="entityParameterPanel?.selectedEntityInfo || {}"
        :panel-position="entityParameterPanel?.panelPosition || { x: 0, y: 0 }"
        @close="entityParameterPanel?.hideParameterPanel"
      />
      <!-- 设备列表 -->
      <EquipListCom v-model="showEquipListFlag" :equipmentList="equipmentList" />
      <!-- 冲突列表 -->
      <ConflictCom
        v-model="showConflictFlag"
        :conflictList="conflictList"
        :war-task-id="warTaskId"
      />
      <!-- 行动记录 -->
      <RecordCom v-model="showRecordFlag" :title="recordTitle" :recordList="recordList" />
      <!-- 日志记录 -->
      <LogCom v-model="showLogFlag" :logList="logList" />
      <!-- 频率甘特图 -->
      <FreqGanttCom
        v-if="showGanttFlag"
        v-model="showGanttFlag"
        :categories="categories"
        :collaboration-ranges="collaborationRanges"
      />
      <!-- RTSP弹窗 -->
      <RtspDialog v-model="showRtspFlag" :rtsp-list="rtspList" />
      <!-- 信噪比统计 -->
      <SnrStatistic
        v-if="showSnrStatisticsFlag"
        v-model="showSnrStatisticsFlag"
        :war-task-id="warTaskId"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  /* 容器全铺 */
  .mapContainer {
    height: calc(100% - 76px);
  }

  .tool-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
    background: rgba(248, 250, 252, 0.6);
    border: 1px solid rgba(226, 232, 240, 0.4);
    transition: all 0.2s ease;
  }
  .tool-btn:hover {
    background: rgba(241, 245, 249, 0.8);
    transform: translateX(-2px);
  }
  .tool-btn:active {
    transform: translateX(0);
  }

  .zoom-item {
    width: 100px;
    height: 30px;
    background: #303336;
    color: #f1c907;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    padding: 0;
  }

  .zoom-item:hover {
    background-color: #4488bb;
    border: 1px solid #aaeeff;
  }
</style>
