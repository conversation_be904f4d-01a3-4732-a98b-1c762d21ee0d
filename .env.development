# 页面标题
VITE_GLOB_APP_TITLE = 频管***软件
VITE_PREVIEWURL='192.168.31.71:9082/onlinePreview?url='
# 开发环境配置
VITE_APP_ENV = 'development'
VITE_PUBLIC_PATH = /
# VITE_GLOB_APP_PREVIEW='************:9082'
# VITE_GLOB_APP_PREVIEW='***********:9082'
# VITE_GLOB_APP_PREVIEW='***********:9086'
VITE_GLOB_APP_PREVIEW='************:9085'
# 管理系统/开发环境
VITE_GLOB_APP_BASE_API = '/dev-api'
# 地图地址
VITE_GLOB_MAP_BASE_API = 'http://*************'
# VITE_PROXY=[["/dev-api","http://************:9082"]]
# VITE_PROXY=[["/dev-api","http://***********:9082"]]
# VITE_PROXY=[["/dev-api","http://***********:9086"]]
VITE_PROXY=[["/dev-api","http://************:9085"]]
# websocket 地址
# VITE_GLOB_WS = 'ws://************:9082'
# VITE_GLOB_WS = 'ws://***********:9082'
# VITE_GLOB_WS = 'ws://***********:9086'
VITE_GLOB_WS = 'ws://************:9085'
# VITE_GLOB_WS = 'ws://127.0.0.1:9082'
VITE_GLOB_HOST = '*************'
VITE_GLOB_PORT = '4444'
# 上位机模式
VITE_GLOB_UPPER_COMPUTER_MODE = 0

#试用环境配置
VITE_APP_TRAIL = 0









